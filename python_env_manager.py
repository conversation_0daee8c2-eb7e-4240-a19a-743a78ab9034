"""
Python环境管理器
用于管理不同版本的Python环境，特别是AutoCali需要Python 3.8的情况
"""
import os
import sys
import subprocess
from pathlib import Path


class PythonEnvManager:
    def __init__(self):
        self.project_dir = Path(__file__).parent
        self.python312_exe = None
        self.python38_exe = None
        self._detect_python_environments()
    
    def _detect_python_environments(self):
        """检测可用的Python环境"""
        # 优先从环境变量读取（由VSETools.bat设置）
        env_python312 = os.environ.get('VSETOOLS_PYTHON312')
        env_python38 = os.environ.get('VSETOOLS_PYTHON38')
        
        if env_python312 and os.path.exists(env_python312):
            self.python312_exe = env_python312
        else:
            # Python 3.12 环境
            winpython312_dir = self.project_dir / "Winpython31210"
            if winpython312_dir.exists():
                python312_path = winpython312_dir / "python" / "python.exe"
                if python312_path.exists():
                    self.python312_exe = str(python312_path)
        
        if env_python38 and os.path.exists(env_python38):
            self.python38_exe = env_python38
        else:
            # Python 3.8 环境
            winpython38_dir = self.project_dir / "Winpython30810"
            if winpython38_dir.exists():
                python38_path = winpython38_dir / "python-3.8.10.amd64" / "python.exe"
                if python38_path.exists():
                    self.python38_exe = str(python38_path)
        
        # 如果没有找到便携版，尝试系统Python
        if not self.python312_exe:
            self.python312_exe = sys.executable
    
    def get_python_for_module(self, module_name):
        """根据模块名返回合适的Python解释器路径"""
        if module_name.lower() in ['autocali', 'autocali_gui', 'autocali_tool']:
            # AutoCali模块需要Python 3.8（支持matlab.engine）
            if self.python38_exe:
                return self.python38_exe
            else:
                print("警告: 未找到Python 3.8环境，AutoCali可能无法使用matlab.engine")
                return self.python312_exe
        else:
            # 其他模块使用Python 3.12
            return self.python312_exe
    
    def run_module_with_correct_python(self, module_name, script_path, *args):
        """使用正确的Python版本运行模块"""
        python_exe = self.get_python_for_module(module_name)
        cmd = [python_exe, script_path] + list(args)
        
        print(f"使用 {python_exe} 运行 {module_name}")
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(self.project_dir) + os.pathsep + env.get('PYTHONPATH', '')
        
        return subprocess.run(cmd, env=env, cwd=str(self.project_dir))
    
    def get_env_info(self):
        """获取环境信息"""
        info = {
            'python312_available': self.python312_exe is not None,
            'python38_available': self.python38_exe is not None,
            'python312_path': self.python312_exe,
            'python38_path': self.python38_exe
        }
        return info


# 全局实例
env_manager = PythonEnvManager()


def get_python_for_autocali():
    """获取AutoCali专用的Python路径"""
    return env_manager.get_python_for_module('autocali')


def get_default_python():
    """获取默认Python路径（3.12）"""
    return env_manager.python312_exe


if __name__ == "__main__":
    # 测试环境检测
    manager = PythonEnvManager()
    info = manager.get_env_info()
    
    print("Python环境检测结果:")
    print(f"Python 3.12 可用: {info['python312_available']}")
    print(f"Python 3.12 路径: {info['python312_path']}")
    print(f"Python 3.8 可用: {info['python38_available']}")
    print(f"Python 3.8 路径: {info['python38_path']}")
    
    print(f"\nAutoCali将使用: {manager.get_python_for_module('autocali')}")
    print(f"其他模块将使用: {manager.get_python_for_module('other')}")