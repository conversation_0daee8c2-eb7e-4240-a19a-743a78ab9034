import os
import shutil
import signal
import subprocess
from RTETestUtils.excelUtil import unzip_file, zip_file
import RTETestUtils.slddUtils
from loguru import logger
import transplant

from IntegrationUtils.load_dbc import loadVSESignalsFromDBC
from IntegrationUtils.load_xlsx_matrix import gen_vse_signals_dict
from loguru import logger
import subprocess
from IntegrationUtils.parseSignalConfig import genSignalFigureFile



def matlab_kill():
    for line in os.popen('tasklist'):
        if 'MATLAB.exe' in line:
            process_info = line.split()
            os.kill(int(process_info[1]), signal.SIGTERM)


def moveIntegrationCode(resourceCodePath, destinationCodePath):
    if not os.path.exists(destinationCodePath):
        logger.error("代码集成环境不存在!")
    for root, dirs, files in os.walk(resourceCodePath):
        for file in files:
            if (file.endswith(".c") and "main" not in file) or file.endswith(".h"):
                fullResouceFilePath = os.path.join(root, file)
                if file.endswith(".c"):
                    fullDestinationPath = os.path.join(destinationCodePath, str.split(file, ".")[0] + ".cpp")
                else:
                    fullDestinationPath = os.path.join(destinationCodePath, file)
                shutil.copy(fullResouceFilePath, fullDestinationPath)

def integrationProcess(VSE_integration_files_path,generate_files_path,fig_sig):
    VSE_code_path = VSE_integration_files_path
    integration_test_folder = "Code_Integration"

    slx_models_path = VSE_integration_files_path
    target_path = generate_files_path
    integrationSLDD = "itegration.sldd"
    integrationSLX = "integration.slx"

    vse_input_signals_config = os.getcwd()+"/IntegrationUtils/vse_dbc_signal.yaml"

    can_matrix = ""
    can_dbc =""
    for filename in os.listdir(VSE_integration_files_path):
        if filename.endswith(".xlsx") and "CAN_Matrix" in filename:
            can_matrix=os.path.join(VSE_integration_files_path,filename)
        if filename.endswith("dbc"):
            can_dbc = os.path.join(VSE_integration_files_path,filename)



    logSignals =fig_sig
    if not os.path.exists(target_path):
        os.mkdir(target_path)
    tmp_slx_path = os.path.join(target_path, "tmp_slx")

    if os.path.exists(tmp_slx_path):
        shutil.rmtree(tmp_slx_path)
    os.mkdir(tmp_slx_path)
    for root, dirs, files in os.walk(slx_models_path):
        for file in files:
            if file.endswith(".slx") or file.endswith(".sldd"):
                target_file = os.path.join(tmp_slx_path, file)
                if os.path.exists(target_file):
                    os.remove(target_file)
                copy_file = os.path.join(root, file)
                shutil.copy(copy_file, target_file)

    sldd_list = []

    for file in os.listdir(tmp_slx_path):
        if file.endswith(".sldd"):
            sldd_list.append(os.path.join(tmp_slx_path, file))
    integrationSLDD_path = os.path.join(target_path, integrationSLDD)
    if os.path.exists(integrationSLDD_path):
        os.remove(integrationSLDD_path)

    matlab = transplant.Matlab(executable=r"D:\Program Files\MATLAB\R2021b\bin\matlab.exe", jvm=True, desktop=False, )
    matlab.addpath(tmp_slx_path)
    matlab.addpath(target_path)
    matlab.addpath(os.getcwd()+"/IntegrationUtils")
    DM_sldd = os.path.join(tmp_slx_path, "VSE_VehDataMngt.sldd")
    ComnDD_sldd = os.path.join(tmp_slx_path, "VSE_ComnDD.sldd")

    matlab.genIntegrationSLDD(integrationSLDD_path, DM_sldd)

    integrationUZip = f"{integrationSLDD_path}UZipDir"

    if os.path.exists(integrationUZip):
        shutil.rmtree(integrationUZip)
    RTETestUtils.excelUtil.unzip_file(integrationSLDD_path, integrationUZip)

    for item in sldd_list:
        RTETestUtils.slddUtils.merge_sldd(item, integrationUZip)
    os.remove(integrationSLDD_path)
    zip_file(integrationUZip, integrationSLDD_path)
    shutil.rmtree(integrationUZip)

    integrationSLX_path = os.path.join(target_path, integrationSLX)

    slx_list = []

    slx_name = os.path.join(tmp_slx_path, "VSE_VehDataMngt")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_SysStMac")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_DynCentre")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_CentroidStEstim")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_VehSpdSlipRateEstim")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_SlopAgEstim")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_SlopYAgEstim")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_VehMEstim")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_RoadAdhCoeffEstim")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_VehPoseEstim")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_SideSlipAgEstim")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_TarYawRateEstim")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_RoadAdhCoeffSplitEstim")
    slx_list.append(slx_name)

    slx_name = os.path.join(tmp_slx_path, "VSE_CnvArbn")
    slx_list.append(slx_name)

    vse_signal_dict = None

    if can_matrix != "" and can_matrix.endswith(".xlsx"):
        vse_signal_dict = gen_vse_signals_dict(can_matrix, vse_input_signals_config, target_path)

    elif can_dbc != "" and can_dbc.endswith(".dbc"):
        vse_signal_dict = loadVSESignalsFromDBC(can_dbc, vse_input_signals_config)
    if vse_signal_dict is None:
        raise Exception("CAN矩阵路径为空！")

    matlab.generateIntegrationModel(integrationSLX.split(".")[0], integrationSLDD, slx_list, vse_signal_dict,logSignals)

    shutil.copy(integrationSLX, integrationSLX_path)
    os.remove(integrationSLX)
    shutil.rmtree(tmp_slx_path)

    matlab_kill()

    os._exit(1)


def integrationTest(VSE_integration_files_path,csv_folder,result_path,fig_sig):
    VSE_code_path = VSE_integration_files_path
    integration_test_folder =os.path.join(os.getcwd(),"IntegrationUtils","Code_Integration")
    moveIntegrationCode(VSE_code_path, integration_test_folder + "/VSE_ASW_Code")
    genSignalFigureFile(fig_sig, integration_test_folder+"\\signalsFigureConfig.h",
                        integration_test_folder+"\\signalsFigureConfig.cpp")
    command = os.path.join(integration_test_folder, "Code_Integration_Test.sln")
    compile_process = subprocess.Popen(f"msbuild.exe {command} /p:Configuration=Release", stdout=subprocess.PIPE,
                                       stderr=subprocess.PIPE)
    compile_process.communicate()
    shutil.copy(integration_test_folder + "/x64/Release/Code_Integration_Test.exe",
                integration_test_folder + "/Code_Integration_Test.exe")
    command = os.path.join(integration_test_folder, "Code_Integration_Test.exe")
    subprocess.Popen([command, csv_folder, result_path], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

