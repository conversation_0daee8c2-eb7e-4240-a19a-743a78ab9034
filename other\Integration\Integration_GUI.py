import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFileDialog, QListWidget, QListWidgetItem, QLineEdit, QGroupBox, QMessageBox, QComboBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class IntegrationGUI(QMainWindow):
    def __init__(self, on_home=None, on_back=None):
        super().__init__()
        self.on_home = on_home
        self.on_back = on_back
        self.setWindowTitle("软件集成工具")
        self.setGeometry(200, 120, 1000, 650)
        self.init_ui()
        self.setup_styles()

    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 10, 20, 20)
        
        # 顶部导航
        nav_container = self.create_nav_bar()
        main_layout.addWidget(nav_container)
        
        # 标题
        title_label = QLabel("软件集成工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin: 10px 0 20px 0;")
        main_layout.addWidget(title_label)
        
        # 区域1：模型集成
        group1 = self.create_model_integration_group()
        main_layout.addWidget(group1)
        
        # 区域2：信号绘图
        group2 = self.create_signal_plotting_group()
        main_layout.addWidget(group2)
        
        # 状态提示
        self.status_label = QLabel("")
        self.status_label.setFont(QFont("Arial", 11))
        self.status_label.setStyleSheet("color: #27ae60; margin: 10px 0; padding: 5px;")
        main_layout.addWidget(self.status_label)
        
        main_layout.addStretch()

    def create_nav_bar(self):
        """创建导航栏"""
        nav_container = QWidget()
        nav_layout = QHBoxLayout(nav_container)
        nav_layout.setContentsMargins(0, 0, 0, 10)
        # 返回上一级按钮（橙色）
        back_btn = QPushButton("返回上一级")
        back_btn.setFont(QFont("Arial", 11))
        back_btn.setFixedSize(120, 36)
        if self.on_back:
            back_btn.clicked.connect(self.on_back)
        else:
            back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        nav_layout.addWidget(back_btn)
        # 返回主页按钮（主色蓝色）
        home_btn = QPushButton("返回主页")
        home_btn.setFont(QFont("Arial", 11))
        home_btn.setFixedSize(100, 32)
        if self.on_home:
            home_btn.clicked.connect(self.on_home)
        else:
            home_btn.clicked.connect(self.close)
        home_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        nav_layout.addWidget(home_btn)
        nav_layout.addStretch()
        
        return nav_container

    def create_model_integration_group(self):
        """创建模型集成分组"""
        group = QGroupBox("1. VSE模型集成")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QVBoxLayout(group)
        layout.setSpacing(12)
        
        # 文件夹选择区域
        folder_widget = QWidget()
        folder_layout = QGridLayout(folder_widget)
        folder_layout.setSpacing(12)
        
        # VSE模型存放地址
        folder_layout.addWidget(QLabel("VSE模型存放地址："), 0, 0)
        self.model_dir_edit = QLineEdit()
        self.model_dir_edit.setFont(QFont("Arial", 11))
        self.model_dir_edit.setReadOnly(True)
        self.model_dir_edit.setPlaceholderText("请选择VSE模型存放目录...")
        folder_layout.addWidget(self.model_dir_edit, 0, 1)
        
        model_browse_btn = QPushButton("浏览")
        model_browse_btn.setFont(QFont("Arial", 11))
        model_browse_btn.setFixedSize(80, 32)
        model_browse_btn.clicked.connect(self.browse_model_dir)
        model_browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        folder_layout.addWidget(model_browse_btn, 0, 2)
        
        # 集成模型生成地址
        folder_layout.addWidget(QLabel("集成模型生成地址："), 1, 0)
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setFont(QFont("Arial", 11))
        self.output_dir_edit.setReadOnly(True)
        self.output_dir_edit.setPlaceholderText("请选择集成模型生成目录...")
        folder_layout.addWidget(self.output_dir_edit, 1, 1)
        
        output_browse_btn = QPushButton("浏览")
        output_browse_btn.setFont(QFont("Arial", 11))
        output_browse_btn.setFixedSize(80, 32)
        output_browse_btn.clicked.connect(self.browse_output_dir)
        output_browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        folder_layout.addWidget(output_browse_btn, 1, 2)
        
        layout.addWidget(folder_widget)
        
        # 执行按钮
        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        self.model_ok_btn = QPushButton("开始集成")
        self.model_ok_btn.setFont(QFont("Arial", 11, QFont.Bold))
        self.model_ok_btn.setFixedSize(120, 36)
        self.model_ok_btn.clicked.connect(self.run_model_integration)
        self.model_ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        btn_layout.addWidget(self.model_ok_btn)
        
        layout.addLayout(btn_layout)
        
        return group

    def create_signal_plotting_group(self):
        """创建信号绘图分组"""
        group = QGroupBox("2. 信号绘图与仿真")
        group.setFont(QFont("Arial", 12, QFont.Bold))
        layout = QVBoxLayout(group)
        layout.setSpacing(12)
        
        # 信号选择区域
        signal_widget = QWidget()
        signal_layout = QHBoxLayout(signal_widget)
        signal_layout.setSpacing(15)
        
        # 左侧：信号列表
        signal_left = QWidget()
        signal_left_layout = QVBoxLayout(signal_left)
        signal_left_layout.addWidget(QLabel("信号选择："))
        self.signal_list = QListWidget()
        self.signal_list.setFont(QFont("Arial", 11))
        self.signal_list.setSelectionMode(QListWidget.MultiSelection)
        self.signal_list.setMaximumHeight(120)
        # 示例信号
        for sig in ["信号A", "信号B", "信号C", "信号D"]:
            item = QListWidgetItem(sig)
            item.setFont(QFont("Arial", 11))
            self.signal_list.addItem(item)
        signal_left_layout.addWidget(self.signal_list)
        signal_layout.addWidget(signal_left)
        
        # 右侧：绘图形式选择
        plot_widget = QWidget()
        plot_layout = QVBoxLayout(plot_widget)
        plot_layout.addWidget(QLabel("绘图形式："))
        self.plot_type_combo = QComboBox()
        self.plot_type_combo.setFont(QFont("Arial", 11))
        self.plot_type_combo.addItems(["折线图", "散点图", "柱状图"])
        plot_layout.addWidget(self.plot_type_combo)
        plot_layout.addStretch()
        signal_layout.addWidget(plot_widget)
        
        layout.addWidget(signal_widget)
        
        # 文件夹选择区域
        folder_widget = QWidget()
        folder_layout = QGridLayout(folder_widget)
        folder_layout.setSpacing(12)
        
        # CSV测试数据文件夹
        folder_layout.addWidget(QLabel("CSV测试数据文件夹："), 0, 0)
        self.csv_dir_edit = QLineEdit()
        self.csv_dir_edit.setFont(QFont("Arial", 11))
        self.csv_dir_edit.setReadOnly(True)
        self.csv_dir_edit.setPlaceholderText("请选择CSV测试数据目录...")
        folder_layout.addWidget(self.csv_dir_edit, 0, 1)
        
        csv_browse_btn = QPushButton("浏览")
        csv_browse_btn.setFont(QFont("Arial", 11))
        csv_browse_btn.setFixedSize(80, 32)
        csv_browse_btn.clicked.connect(self.browse_csv_dir)
        csv_browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        folder_layout.addWidget(csv_browse_btn, 0, 2)
        
        # 生成仿真图片文件夹
        folder_layout.addWidget(QLabel("生成仿真图片文件夹："), 1, 0)
        self.img_dir_edit = QLineEdit()
        self.img_dir_edit.setFont(QFont("Arial", 11))
        self.img_dir_edit.setReadOnly(True)
        self.img_dir_edit.setPlaceholderText("请选择仿真图片保存目录...")
        folder_layout.addWidget(self.img_dir_edit, 1, 1)
        
        img_browse_btn = QPushButton("浏览")
        img_browse_btn.setFont(QFont("Arial", 11))
        img_browse_btn.setFixedSize(80, 32)
        img_browse_btn.clicked.connect(self.browse_img_dir)
        img_browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        folder_layout.addWidget(img_browse_btn, 1, 2)
        
        layout.addWidget(folder_widget)
        
        # 执行按钮
        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        self.sim_ok_btn = QPushButton("开始仿真")
        self.sim_ok_btn.setFont(QFont("Arial", 11, QFont.Bold))
        self.sim_ok_btn.setFixedSize(120, 36)
        self.sim_ok_btn.clicked.connect(self.run_simulation)
        self.sim_ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        btn_layout.addWidget(self.sim_ok_btn)
        
        layout.addLayout(btn_layout)
        
        return group

    def setup_styles(self):
        """设置整体样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QWidget {
                background-color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin: 10px 0;
                padding-top: 15px;
                color: #2c3e50;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px;
                background-color: #ecf0f1;
            }
            QLabel {
                color: #2c3e50;
                font-size: 11pt;
                font-weight: normal;
            }
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 6px;
                font-size: 11pt;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QComboBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 6px;
                font-size: 11pt;
                background-color: white;
            }
            QListWidget {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
                font-size: 11pt;
            }
        """)

    def browse_model_dir(self):
        path = QFileDialog.getExistingDirectory(self, "选择VSE模型存放地址")
        if path:
            self.model_dir_edit.setText(path)

    def browse_output_dir(self):
        path = QFileDialog.getExistingDirectory(self, "选择集成模型生成地址")
        if path:
            self.output_dir_edit.setText(path)

    def run_model_integration(self):
        # 这里应调用实际集成程序
        self.status_label.setText("✓ 模型集成已完成！")
        QMessageBox.information(self, "提示", "模型集成已完成！")

    def browse_csv_dir(self):
        path = QFileDialog.getExistingDirectory(self, "选择CSV测试数据文件夹")
        if path:
            self.csv_dir_edit.setText(path)

    def browse_img_dir(self):
        path = QFileDialog.getExistingDirectory(self, "选择生成仿真图片文件夹")
        if path:
            self.img_dir_edit.setText(path)

    def run_simulation(self):
        # 这里应调用实际仿真绘图程序
        self.status_label.setText("✓ 仿真图片生成已完成！")
        QMessageBox.information(self, "提示", "仿真图片生成已完成！")

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    window = IntegrationGUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
