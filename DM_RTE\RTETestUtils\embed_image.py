from RTETestUtils.excelUtil import embed_image
import os
import re
from openpyxl import load_workbook


def insert_image2Excel(image_path: str, excel_path: str):
    chinese_pattern = re.compile(r'[\u4e00-\u9fa5]+')
    num_pattern = re.compile(r'[a-z_A-Z0-9\-]+')

    wb = load_workbook(excel_path)
    sheet_name = wb.sheetnames[4]
    ws = wb[sheet_name]

    row = 1

    for f in os.listdir(image_path):
        if f.endswith(".png") or f.endswith(".jpg") or f.endswith(".jpeg"):
            chinese_march = chinese_pattern.findall(f)[0]
            num_march = num_pattern.findall(f)[0]
            i = 2
            num_exist = False
            while i <= row:
                if ws["I" + str(i)].value == num_march:
                    num_exist = True
                    if "车速" in chinese_march:
                        ws.row_dimensions[i].height = 127
                        ws.column_dimensions["O"].width = 30
                        ws["O" + str(i)].value = os.path.join(image_path, f)
                    elif "滑移率" in chinese_march:
                        ws["P" + str(i)].value = os.path.join(image_path, f)
                        ws.row_dimensions[i].height = 127
                        ws.column_dimensions["P"].width = 30
                    elif "附着系数" in chinese_march:
                        ws["Q" + str(i)].value = os.path.join(image_path, f)
                        ws.row_dimensions[i].height = 127
                        ws.column_dimensions["Q"].width = 30
                    elif "质心侧偏角" in chinese_march:
                        ws["R" + str(i)].value = os.path.join(image_path, f)
                        ws.row_dimensions[i].height = 127
                        ws.column_dimensions["R"].width = 30
                    elif "目标横摆角速度" in chinese_march:
                        ws["S" + str(i)].value = os.path.join(image_path, f)
                        ws.row_dimensions[i].height = 127
                        ws.column_dimensions["S"].width = 30
                    elif "纵坡" in chinese_march:
                        ws["T" + str(i)].value = os.path.join(image_path, f)
                        ws.row_dimensions[i].height = 127
                        ws.column_dimensions["T"].width = 30
                    elif "横坡" in chinese_march:
                        ws["U" + str(i)].value = os.path.join(image_path, f)
                        ws.row_dimensions[i].height = 127
                        ws.column_dimensions["U"].width = 30
                    elif "车轮载荷" in chinese_march:
                        ws["V" + str(i)].value = os.path.join(image_path, f)
                        ws.row_dimensions[i].height = 127
                        ws.column_dimensions["V"].width = 30
                    elif "质量" in chinese_march:
                        ws["W" + str(i)].value = os.path.join(image_path, f)
                        ws.row_dimensions[i].height = 127
                        ws.column_dimensions["W"].width = 30
                    break
                i = i + 1
            if not num_exist:
                row = row + 1
                ws["I" + str(row)].value = num_march
                ws["A" + str(row)].value = row - 1
                ws["B" + str(row)].value = "case00" + str(row - 1)
                ws["C" + str(row)].value = "VSEAA2.0-1009；"
                ws["D" + str(row)].value = "基于需求测试"
                ws["E" + str(row)].value = "需求分析\n外部接口分析\n边界值分析\n功能的相关性分析\n等价类分析"
                ws["F" + str(row)].value = "功能需求"
                ws["G" + str(row)].value = "轮速、车速及滑移率估计；"
                ws["H" + str(row)].value = "1"
                ws["J" + str(row)].value = "1，实车通讯正常、软件在OBA上运行\n2，整车上ok电，并能正常行驶\n3.回放数据，RAC018棋盘路直线全油门"
                ws["K" + str(row)].value = "1.整车上电，采集VSE实车输出及输入信号\n2.将实车数据回灌到控制器\n3.观测信号：使用CANoe回灌数据，CANape" \
                                           "观测输出车速VSE_VSRE_fVxEst与四轮轮速及运行周期与运行时间\n"
                ws["L" + str(row)].value = "回放整车数据观测车速及轮速等信号"
                ws["M" + str(row)].value = "Pass"
                if "车速" in chinese_march:
                    ws.row_dimensions[row].height = 127
                    ws.column_dimensions["O"].width = 30
                    ws["O" + str(row)].value = os.path.join(image_path, f)
                elif "滑移率" in chinese_march:
                    ws["P" + str(row)].value = os.path.join(image_path, f)
                    ws.row_dimensions[row].height = 127
                    ws.column_dimensions["P"].width = 30
                elif "附着系数" in chinese_march:
                    ws["Q" + str(row)].value = os.path.join(image_path, f)
                    ws.row_dimensions[row].height = 127
                    ws.column_dimensions["Q"].width = 30
                elif "质心侧偏角" in chinese_march:
                    ws["R" + str(row)].value = os.path.join(image_path, f)
                    ws.row_dimensions[row].height = 127
                    ws.column_dimensions["R"].width = 30
                elif "目标横摆角速度" in chinese_march:
                    ws["S" + str(row)].value = os.path.join(image_path, f)
                    ws.row_dimensions[row].height = 127
                    ws.column_dimensions["S"].width = 30
                elif "纵坡" in chinese_march:
                    ws["T" + str(row)].value = os.path.join(image_path, f)
                    ws.row_dimensions[row].height = 127
                    ws.column_dimensions["T"].width = 30
                elif "横坡" in chinese_march:
                    ws["U" + str(row)].value = os.path.join(image_path, f)
                    ws.row_dimensions[row].height = 127
                    ws.column_dimensions["U"].width = 30
                elif "车轮载荷" in chinese_march:
                    ws["V" + str(row)].value = os.path.join(image_path, f)
                    ws.row_dimensions[row].height = 127
                    ws.column_dimensions["V"].width = 30
                elif "质量" in chinese_march:
                    ws["W" + str(row)].value = os.path.join(image_path, f)
                    ws.row_dimensions[row].height = 127
                    ws.column_dimensions["W"].width = 50
    wb.save(excel_path)
    dir_path = os.path.dirname(excel_path)
    excel_name = os.path.basename(excel_path)
    tmp_excel = os.path.join(dir_path, "tmp.xlsx")
    embed_image(excel_path, tmp_excel,
                "测试用例及结果",
                ["车速", "滑移率", "附着系数", "质心侧偏角", "目标横摆角速度", "纵坡", "横坡", "车轮载荷", "质量"])
    os.remove(excel_path)
    os.rename(tmp_excel, excel_path)



if __name__ == '__main__':
    insert_image2Excel(r"E:\新建文件夹\SGHL_D3回灌2",
                       r"E:\新建文件夹 (2)\VSEAA 2.0 EXEA-OBA车型软件集成点检测试报告V1.0.0 - 副本.xlsx")
