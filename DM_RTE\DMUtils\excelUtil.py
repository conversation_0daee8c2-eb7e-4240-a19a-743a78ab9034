import os
import shutil
import zipfile
from shlex import shlex

import openpyxl
from uuid import uuid4

import lxml
import pandas as pd
from PIL import Image
from loguru import logger
from lxml import etree
import getpass
import time
from DMUtils.slxUtils import carInfoDict


user_map={"su.xin2":"苏鑫","bi.sheng1":"毕升","zeng.gujie":"曾顾杰",
          "fang.haiji":"方海积","fang.haiji":"冯建川","hou.ruiyu":"侯瑞宇",
          "hu.jie31":"胡杰","hu.qixun":"胡奇勋","jiang.xinxiu":"蒋新秀",
          "liu.junfu2":"刘隽夫","liu.yuzhu3":"刘玉柱","luo.ji2":"罗基",
          "mei.shuchi":"梅述池","shi.dengke":"史登科","wang.ke44":"王轲",
          "xu.rui21":"许睿","yang.nannan2":"杨楠楠","you.yang4":"游洋",
          "zhang.zezhong":"张泽中","yao.zhixiao":"姚智晓","zhao.wanshuan":"赵万栓",
          "yin.shaodong":"尹邵东",}


cellimages_rels_template_content = """
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
</Relationships>
"""

cellimages_template_content = """
<etc:cellImages xmlns:xdr="http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing"
                xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
                xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" xmlns:etc="http://www.wps.cn/officeDocument/2017/etCustomData">

</etc:cellImages>
"""


def unzip_file(zip_path: str, extract_to: str):
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_to)
    logger.debug(f"Extracted {zip_path} to {extract_to}")


def zip_file(file_or_dir_path: str, zip_path: str):
    # Create a ZipFile object in write mode
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        if os.path.isfile(file_or_dir_path):
            # If the path is a file, write it to the zip file
            zipf.write(file_or_dir_path, os.path.basename(file_or_dir_path))
        elif os.path.isdir(file_or_dir_path):
            # If the path is a directory, walk through the directory
            for root, dirs, files in os.walk(file_or_dir_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, file_or_dir_path)
                    zipf.write(file_path, arcname)
    logger.debug(f"Compressed {file_or_dir_path} to {zip_path}")


def get_image_dimensions(image_path: str):
    with Image.open(image_path) as img:
        width, height = img.size
        logger.debug(f"Image dimensions: width={width}, height={height}")
        return width, height


def copy_image_to_excel_dir(image_path: str, excel_unzip_dir: str, ID: str):
    media_dir = os.path.join(excel_unzip_dir, 'xl', 'media')
    if not os.path.exists(media_dir):
        os.makedirs(media_dir)

    image_filename = os.path.basename(image_path)
    image_type = str.split(image_filename, ".")[1]

    destination_path = os.path.join(media_dir, ID + "." + image_type)
    shutil.copy(image_path, destination_path)
    logger.debug(f"Copied {image_path} to {destination_path}")
    return ID + "." + image_type, destination_path


def add_new_node_cell_images(image_path: str, excel_unzip_dir: str):
    # 指定xml是否存在 excel中未插入过图片则不存在 需要补充进去
    xmlPath = os.path.join(excel_unzip_dir, 'xl', 'cellimages.xml')
    if not os.path.exists(xmlPath):
        # 模板内容写入
        with open(xmlPath, 'w', encoding='utf-8') as f:
            f.write(cellimages_template_content)

    uuid = str(uuid4()).replace('-', '').upper()
    # 创建解析器时关闭 DTD 和校验
    parser = etree.XMLParser(
        load_dtd=False,  # 不加载 DTD
        no_network=True,  # 禁止网络请求（防止自动下载 DTD）
        dtd_validation=False,  # 关闭 DTD 校验
        attribute_defaults=False,
        recover=True
    )
    tree = etree.parse(xmlPath, parser=parser)
    root = tree.getroot()
    # 提取命名空间
    namespaces = {prefix: uri for prefix, uri in root.nsmap.items() if prefix is not None}
    logger.debug(f"Extracted namespaces: {namespaces}")

    count = len(root.findall('.//xdr:pic', namespaces=namespaces))
    ID = f"ID_{uuid}"
    RID = f"rId{count + 1}"
    width, height = get_image_dimensions(image_path)
    image_name = str.split(os.path.basename(image_path), ".")[0]
    xml_string = f"""
    <etc:cellImage>
        <xdr:pic>
            <xdr:nvPicPr>
                <xdr:cNvPr id="{count + 1}" name="{ID}" descr="{image_name}"/>
                <xdr:cNvPicPr>
                    <a:picLocks noChangeAspect="1"/>
                <xdr:cNvPicPr/>
            </xdr:nvPicPr>
            <xdr:blipFill>
                <a:blip r:embed="{RID}"/>
                <a:stretch>
                    <a:fillRect/>
                </a:stretch>
            </xdr:blipFill>
            <xdr:spPr>
                <a:xfrm>
                    <a:off x="5638800" y="180975"/>
                    <a:ext cx="5852160" cy="4389120"/>
                </a:xfrm>
                <a:prstGeom prst="rect">
                    <a:avLst/>
                </a:prstGeom>
            </xdr:spPr>
        </xdr:pic>
    </etc:cellImage>
    """
    new_cell_image = etree.fromstring(xml_string, parser=parser)
    root.append(new_cell_image)
    tree.write(xmlPath, pretty_print=True, xml_declaration=True, encoding='UTF-8')

    return ID, RID


def add_new_node_cell_images_rels(image_file_name: str, RID: str, excel_unzip_dir: str):
    # 指定xml是否存在 excel中未插入过图片则不存在 需要补充进去
    xmlPath = os.path.join(excel_unzip_dir, 'xl', '_rels', 'cellimages.xml.rels')
    if not os.path.exists(xmlPath):
        # 模板内容写入
        with open(xmlPath, 'w', encoding='utf-8') as f:
            f.write(cellimages_rels_template_content)

    # 创建解析器时关闭 DTD 和校验
    parser = etree.XMLParser(
        load_dtd=False,  # 不加载 DTD
        no_network=True,  # 禁止网络请求（防止自动下载 DTD）
        dtd_validation=False,  # 关闭 DTD 校验
        attribute_defaults=False,
        recover=True
    )
    tmp_strs = str.split(image_file_name, ".")
    xml_string = f"""
       <Relationship Id="{RID}" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image" Target="media/{image_file_name}"/>
    """
    new_cell_image = etree.fromstring(xml_string, parser=parser)
    tree = etree.parse(xmlPath, parser=parser)
    tree.getroot().append(new_cell_image)
    tree.write(xmlPath, pretty_print=True, xml_declaration=True, encoding='UTF-8')


def add_new_node_content_types(excel_unzip_dir: str, sheet_Id: int):
    # 解析 XML 文件
    xmlPath = os.path.join(excel_unzip_dir, '[Content_Types].xml')
    # 创建解析器时关闭 DTD 和校验
    parser = etree.XMLParser(
        load_dtd=False,  # 不加载 DTD
        no_network=True,  # 禁止网络请求（防止自动下载 DTD）
        dtd_validation=False,  # 关闭 DTD 校验
        attribute_defaults=False,
        recover=True
    )
    tree = etree.parse(xmlPath, parser=parser)
    root = tree.getroot()
    if root.find('{http://schemas.openxmlformats.org/package/2006/content-types}Default[@Extension="JPG"]') is None:
        xml_string = f"""
                 <Default Extension="JPG" ContentType="image/.jpg"/>
            """
        new_cell_image = etree.fromstring(xml_string, parser=parser)
        tree.getroot().append(new_cell_image)
    if root.find('{http://schemas.openxmlformats.org/package/2006/content-types}Default[@Extension="jpeg"]') is None:
        xml_string = f"""
                 <Default Extension="jpeg" ContentType="image/jpeg"/>
            """
        new_cell_image = etree.fromstring(xml_string, parser=parser)
        tree.getroot().append(new_cell_image)

    if root.find('{http://schemas.openxmlformats.org/package/2006/content-types}Default[@Extension="png"]') is None:
        xml_string = f"""
                 <Default Extension="png" ContentType="image/png"/>
            """
        new_cell_image = etree.fromstring(xml_string, parser=parser)
        tree.getroot().append(new_cell_image)

    if root.find(
            '{http://schemas.openxmlformats.org/package/2006/content-types}Override[@PartName="/xl/cellimages.xml"]') is None:
        xml_string = f"""
                 <Override PartName="/xl/cellimages.xml" ContentType="application/vnd.wps-officedocument.cellimage+xml"/>
            """

        new_cell_image = etree.fromstring(xml_string, parser=parser)
        tree.getroot().append(new_cell_image)
    if root.find(
            '{http://schemas.openxmlformats.org/package/2006/content-types}' + f"""Override[@PartName="/xl/worksheets/sheet{sheet_Id + 1}.xml"]""") is None:
        xml_string = f"""
                        <Override PartName="/xl/worksheets/sheet{sheet_Id + 1}.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>
                 """
        new_cell_image = etree.fromstring(xml_string, parser=parser)
        tree.getroot().append(new_cell_image)

    tree.write(xmlPath, pretty_print=True, xml_declaration=True, encoding='UTF-8')


def add_new_node_workbook(excel_unzip_dir: str):
    # 解析 XML 文件
    xmlPath = os.path.join(excel_unzip_dir, 'xl', '_rels', 'workbook.xml.rels')
    # 创建解析器时关闭 DTD 和校验
    parser = etree.XMLParser(
        load_dtd=False,  # 不加载 DTD
        no_network=True,  # 禁止网络请求（防止自动下载 DTD）
        dtd_validation=False,  # 关闭 DTD 校验
        attribute_defaults=False,
        recover=True
    )
    tree = etree.parse(xmlPath, parser=parser)
    root = tree.getroot()
    if root.find(
            '{http://schemas.openxmlformats.org/package/2006/relationships}Relationship[@Target="cellimages.xml"]') is None:
        xml_string = f"""
                 <Relationship Id="rId100" Type="http://www.wps.cn/officeDocument/2020/cellImage" Target="cellimages.xml"/>
            """
        new_cell_image = etree.fromstring(xml_string, parser=parser)
        tree.getroot().append(new_cell_image)

    tree.write(xmlPath, pretty_print=True, xml_declaration=True, encoding='UTF-8')


def add_new_node(image_path: str, unzip_dir_path: str, sheet_ID: int):
    add_new_node_content_types(unzip_dir_path, sheet_ID)
    add_new_node_workbook(unzip_dir_path)

    ID, RID = add_new_node_cell_images(image_path, unzip_dir_path)
    image_name, image_path = copy_image_to_excel_dir(image_path, unzip_dir_path, ID)
    add_new_node_cell_images_rels(image_name, RID, unzip_dir_path)
    return ID


def add_sheet_data(unzip_file_path, sheet_name, ID, cell_index, row_index, sheet_ID: int):
    # 创建解析器时关闭 DTD 和校验
    parser = etree.XMLParser(
        load_dtd=False,  # 不加载 DTD
        no_network=True,  # 禁止网络请求（防止自动下载 DTD）
        dtd_validation=False,  # 关闭 DTD 校验
        attribute_defaults=False,
        recover=True
    )
    xmlPath = os.path.join(unzip_file_path, 'xl', 'worksheets', f'sheet{sheet_ID+1}.xml')
    tree = etree.parse(xmlPath, parser=parser)
    root = tree.getroot()
    sheetDataTag = root.find('{http://schemas.openxmlformats.org/spreadsheetml/2006/main}sheetData')
    logger.debug(sheetDataTag)
    rowTag = sheetDataTag.findall('{http://schemas.openxmlformats.org/spreadsheetml/2006/main}row')[row_index]
    cellTag: lxml.etree._Element = rowTag.findall('{http://schemas.openxmlformats.org/spreadsheetml/2006/main}c')[
        cell_index]
    r = cellTag.attrib['r']
    cellTag.clear()
    xml_string = f"""
                <f>_xlfn.DISPIMG(&quot;{ID}&quot;,1)</f>
    """
    new_tag = etree.fromstring(xml_string, parser=parser)
    cellTag.append(new_tag)
    xml_string = f"""
               <v>=DISPIMG(&quot;{ID}&quot;,1)</v>
    """
    new_tag = etree.fromstring(xml_string, parser=parser)
    cellTag.append(new_tag)
    cellTag.attrib['r'] = r
    cellTag.attrib['t'] = "str"
    # cellTag.remove('{http://schemas.openxmlformats.org/spreadsheetml/2006/main}v')
    tree.write(xmlPath, pretty_print=True, xml_declaration=True, encoding='UTF-8')


def embed_image(excel_path: str, new_excel_path: str, sheet_name: str, head_name: list):
    # 解压excel
    unzip_dir_path = f"{excel_path}excelUnZipDir"
    if os.path.exists(unzip_dir_path):
        shutil.rmtree(unzip_dir_path)
    unzip_file(excel_path, unzip_dir_path)
    # 读取Excel文件
    df = pd.read_excel(excel_path, sheet_name=sheet_name)
    wb = openpyxl.load_workbook(excel_path)
    sheet_ID = wb.sheetnames.index(sheet_name)
    for i in head_name:
        column_index = df.columns.get_loc(i)
        logger.debug(f"Column '{i}' is at index: {column_index}")
        for index in range(len(df.get(i))):
            pic_local_path = str(df.get(i)[index])
            logger.debug(f"Picture row: {pic_local_path}")
            if pic_local_path is None or not os.path.exists(pic_local_path):
                logger.debug(f"图片文件 {pic_local_path} 不存在")
                continue
            ID = add_new_node(pic_local_path, unzip_dir_path, sheet_ID)
            add_sheet_data(unzip_dir_path, sheet_name, ID, column_index, index + 1, sheet_ID)
    zip_file(unzip_dir_path, new_excel_path)

    if os.path.exists(unzip_dir_path):
        shutil.rmtree(unzip_dir_path)

def modifyExcelCellValue(row_index,cell_index,sheet_root,value,value_sheet):
    sheetDataTag =sheet_root.find('{http://schemas.openxmlformats.org/spreadsheetml/2006/main}sheetData')
    rowTag = sheetDataTag.find('{http://schemas.openxmlformats.org/spreadsheetml/2006/main}row'+f"[@r='{row_index}']")
    cell:lxml.etree._Element=rowTag.find('{http://schemas.openxmlformats.org/spreadsheetml/2006/main}c'+f"[@r='{cell_index}']")
    cell_value=cell.find('{http://schemas.openxmlformats.org/spreadsheetml/2006/main}v')

    stringDataTag=value_sheet.findall('{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si')
    i=0
    for item in stringDataTag:
        if i==int(cell_value.text):
            v=item.find('{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
            v.text=value
            return
        i=i+1
    #

def writeDMExcelTemplate(file_path,car_name,vse_version,key_info_file_path,result_file_path):


    unzip_dir_path = f"{file_path}excelUnZipDir"
    if os.path.exists(unzip_dir_path):
        shutil.rmtree(unzip_dir_path)
    unzip_file(file_path, unzip_dir_path)

    xmlPath = os.path.join(unzip_dir_path, 'xl', 'worksheets','sheet1.xml')
    parser = etree.XMLParser(
        load_dtd=False,  # 不加载 DTD
        no_network=True,  # 禁止网络请求（防止自动下载 DTD）
        dtd_validation=False,  # 关闭 DTD 校验
        attribute_defaults=False,
        recover=True
    )
    sheet_tree = etree.parse(xmlPath, parser=parser)
    sheet_root = sheet_tree.getroot()

    xmlStringPath=os.path.join(unzip_dir_path, 'xl', 'sharedStrings.xml')
    string_tree=etree.parse(xmlStringPath,parser=parser)
    string_root=string_tree.getroot()

    current_time = time.strftime("%Y年%m月%d日", time.localtime())
    modifyExcelCellValue(9, "A9", sheet_root, f"VSE2.0项目{car_name}车型模型差异性分析报告", string_tree)
    modifyExcelCellValue(28, "A28", sheet_root, current_time, string_tree)

    xmlPath = os.path.join(unzip_dir_path, 'xl', 'worksheets', 'sheet2.xml')
    sheet_tree = etree.parse(xmlPath, parser=parser)
    sheet_root = sheet_tree.getroot()

    modifyExcelCellValue(2, "C2", sheet_root,user_map[getpass.getuser()] , string_tree)
    current_time = time.strftime("%Y/%m/%d日", time.localtime())
    modifyExcelCellValue(3, "C3", sheet_root, current_time, string_tree)
    modifyExcelCellValue(3, "E3", sheet_root, current_time, string_tree)
    modifyExcelCellValue(3, "G3", sheet_root, current_time, string_tree)
    modifyExcelCellValue(13, "D13", sheet_root, current_time, string_tree)
    modifyExcelCellValue(13, "E13", sheet_root, user_map[getpass.getuser()], string_tree)

    xmlPath = os.path.join(unzip_dir_path, 'xl', 'worksheets', 'sheet3.xml')
    sheet_tree = etree.parse(xmlPath, parser=parser)
    sheet_root = sheet_tree.getroot()

    modifyExcelCellValue(5, "A5", sheet_root,f"    VSE2.0项目当前适配{car_name}，由于车型发生了表更，VSE项目数据管理模型需要按需求同步进行更新，该文档说明此次适配进行了哪些修改，确保软件释放正常进行", string_tree)

    xmlPath = os.path.join(unzip_dir_path, 'xl', 'worksheets', 'sheet4.xml')
    sheet_tree = etree.parse(xmlPath, parser=parser)
    sheet_root = sheet_tree.getroot()

    modifyExcelCellValue(3, "E3", sheet_root,f"1. 版本号升版更新为 {vse_version}", string_tree)

    str = f"2. {car_name}车型配置字更新\n——>《{key_info_file_path}》\n\n"

    for key in carInfoDict:
        for item in carInfoDict[key]:
            str += f"{item.carCode}-{item.carConfig}:{item.carName}\n"

    modifyExcelCellValue(4, "E4", sheet_root, str, string_tree)

    string_tree.write(xmlStringPath, pretty_print=True, xml_declaration=True, encoding='UTF-8')

    test_file_path=os.path.join(unzip_dir_path,'xl','embeddings','Workbook1.xlsx')
    os.remove(test_file_path)
    shutil.copy(result_file_path,test_file_path)

    os.remove(file_path)
    tstr=file_path+".xlsx"
    bas_str=os.path.dirname(tstr)
    zip_file(unzip_dir_path, bas_str+f"\VSEAA2.0-SWE3-035_VSE2.0项目{car_name}车型差异性分析报告V1.0.0.xlsx")
    shutil.rmtree(unzip_dir_path)

if __name__ == '__main__':
    #zip_file("E:\项目\RTE应用\VSEAA2.0-SWE3-035_VSE2.0项目HT（春改）-D3车型差异性分析报告V1.0.0", "tt.xlsx")
    writeDMExcelTemplate("VSEAA2.0-SWE3-035_VSE2.0项目HT（春改）-D3车型差异性分析报告V1.0.0.xlsx","HT(春改)-D3","VSE2.00.40","关键信息",r"E:\c学习\DM_Auto_Test\x64\Debug\测试结果.xlsx")
