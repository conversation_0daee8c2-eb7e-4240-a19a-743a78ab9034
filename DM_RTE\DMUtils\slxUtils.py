import pandas as pd

import os
from lxml import etree
import shutil
from loguru import logger
import DMUtils.excelUtil

u8CCSoftVersNr = ["u8CCSoftVersNr.Char_Zero", "u8CCSoftVersNr.Char_One", "u8CCSoftVersNr.Char_Two",
                  "u8CCSoftVersNr.Char_Three", "u8CCSoftVersNr.Char_Four", "u8CCSoftVersNr.Char_Five",
                  "u8CCSoftVersNr.Char_Six", "u8CCSoftVersNr.Char_Seven", "u8CCSoftVersNr.Char_Eight",
                  "u8CCSoftVersNr.Char_Nine"]

carInfoDict = dict()


class CarInfo:
    def __init__(self, carCode, carConfig, carName):
        self.carCode = carCode
        self.carConfig = carConfig
        self.carName = carName

    def __str__(self):
        return self.carCode + "  " + self.carConfig + "  " + self.carName


def genCarIDInfo(carIDFile):
    excelData = pd.read_excel(carIDFile, "车型配置对应关系",dtype=str)
    car_config = pd.read_excel(carIDFile, "整车参数",dtype=str)
    l = []
    for item in car_config.iloc[0]:
        if isinstance(item, str) and ("型" in item):
            l.append(item)
    for index, row in excelData.iterrows():
        carType = row.iloc[2]
        if isinstance(carType, str) and "对应" not in carType:
            carIndex = l.index(row.iloc[2]) + 1
            carCode = row.iloc[0]
            carConfig = row.iloc[1]
            if isinstance(carCode, float):
                carCode = str(int(carCode))
            if isinstance(carConfig, float):
                carConfig = str(int(carConfig))
            tmpCarInfo = CarInfo(carCode, carConfig, row.iloc[2])
            if carIndex not in carInfoDict:
                tmpCarInfoSet = set()
                tmpCarInfoSet.add(tmpCarInfo)
                carInfoDict[carIndex] = tmpCarInfoSet
            else:
                tmpCarInfoSet = carInfoDict.get(carIndex)
                tmpCarInfoSet.add(tmpCarInfo)
                carInfoDict[carIndex] = tmpCarInfoSet

    tmpStr = "function carID = getcarID(g_VSE_In_Mem_u16CarCod_1, g_VSE_In_Mem_u8CarCod_2)\n\ncarcode = g_VSE_In_Mem_u16CarCod_1;\ncarvar = g_VSE_In_Mem_u8CarCod_2;\n\n"
    i = 0
    for k in carInfoDict:
        j = 0
        carName = ""
        for info in carInfoDict[k]:
            if i == 0:
                if len(carInfoDict[k]) > 1:
                    if j == 0:
                        tmpStr += f"if (carcode== uint16(hex2dec('{info.carCode}'))&&carvar==uint8(hex2dec('{info.carConfig}')))...\n"
                    elif j < len(carInfoDict[k]) - 1:
                        tmpStr += f"  || (carcode== uint16(hex2dec('{info.carCode}'))&&carvar==uint8(hex2dec('{info.carConfig}')))...\n"
                    else:
                        tmpStr += f" || (carcode== uint16(hex2dec('{info.carCode}'))&&carvar==uint8(hex2dec('{info.carConfig}')))\n"
                else:
                    tmpStr += f"if (carcode== uint16(hex2dec('{info.carCode}'))&&carvar==uint8(hex2dec('{info.carConfig}')))\n"
            else:
                if len(carInfoDict[k]) > 1:
                    if j == 0:
                        tmpStr += f"elseif (carcode== uint16(hex2dec('{info.carCode}'))&&carvar==uint8(hex2dec('{info.carConfig}')))...\n"
                    elif j < len(carInfoDict[k]) - 1:
                        tmpStr += f"  || (carcode== uint16(hex2dec('{info.carCode}'))&&carvar==uint8(hex2dec('{info.carConfig}')))...\n"
                    else:
                        tmpStr += f"  || (carcode== uint16(hex2dec('{info.carCode}'))&&carvar==uint8(hex2dec('{info.carConfig}')))\n"
                else:
                    tmpStr += f"elseif (carcode== uint16(hex2dec('{info.carCode}'))&&carvar==uint8(hex2dec('{info.carConfig}')))\n"
            carName = info.carName
            j = j + 1
        tmpStr += f"    carID=uint8({k});     % {carName}\n"
        i = i + 1

    tmpStr += "else\n    carID=uint8(1);\nend\n"
    return tmpStr


def changeGetCarIDFcn(slxFile, carInfo, version):
    unzip_dir_path = f"{slxFile}UnZipDir"
    if os.path.exists(unzip_dir_path):
        shutil.rmtree(unzip_dir_path)
    DMUtils.excelUtil.unzip_file(slxFile, unzip_dir_path)
    xmlPath = os.path.join(unzip_dir_path, 'simulink', 'stateflow.xml')

    parser = etree.XMLParser(
        load_dtd=False,  # 不加载 DTD
        no_network=True,  # 禁止网络请求（防止自动下载 DTD）
        dtd_validation=False,  # 关闭 DTD 校验
        attribute_defaults=False,
        recover=True
    )
    try:
        tree = etree.parse(xmlPath, parser=parser)
        root = tree.getroot()
        p = root.find("machine")
        p = p.find("Children")
        p = p.find(f".//chart[@id='494'].//Children")
        p = p.find(f".//state")
        p = p.find(f".//eml")
        p = p.find(f".//P[@Name='script']")
        # print(p.text)
        p.text = carInfo
        tree.write(xmlPath)
    except etree.XMLSyntaxError as e:
        print(e)

    xmlPath = os.path.join(unzip_dir_path, 'simulink', 'blockdiagram.xml')

    chars = list(version)
    try:
        tree = etree.parse(xmlPath, parser=parser)
        root = tree.getroot()
        Value = root.find(".//Block[@Name='Enumerated\nConstant9']").find(".//P[@Name='Value']")
        if chars[0] == 'V' or chars[0] == 'v':
            Value.text = "u8CCSoftVersNr.Char_V"
        else:
            raise ValueError("非VSE版本号")

        Value = root.find(".//Block[@Name='Enumerated\nConstant']").find(".//P[@Name='Value']")
        if chars[1] == 'S' or chars[1] == 's':
            Value.text = "u8CCSoftVersNr.Char_S"
        else:
            raise ValueError("非VSE版本号")

        Value = root.find(".//Block[@Name='Enumerated\nConstant1']").find(".//P[@Name='Value']")
        if chars[2] == 'E' or chars[2] == 'e':
            Value.text = "u8CCSoftVersNr.Char_E"
        else:
            raise ValueError("非VSE版本号")

        Value = root.find(".//Block[@Name='Enumerated\nConstant2']").find(".//P[@Name='Value']")
        if chars[3].isdigit():
            Value.text = u8CCSoftVersNr[int(chars[3])]
        else:
            raise ValueError("非VSE版本号")

        Value = root.find(".//Block[@Name='Enumerated\nConstant3']").find(".//P[@Name='Value']")
        if chars[4] == '.':
            Value.text = "u8CCSoftVersNr.Char_Point"
        else:
            raise ValueError("非VSE版本号")

        Value = root.find(".//Block[@Name='Enumerated\nConstant4']").find(".//P[@Name='Value']")
        if chars[5].isdigit():
            Value.text = u8CCSoftVersNr[int(chars[5])]
        else:
            raise ValueError("非VSE版本号")

        Value = root.find(".//Block[@Name='Enumerated\nConstant5']").find(".//P[@Name='Value']")
        if chars[6].isdigit():
            Value.text = u8CCSoftVersNr[int(chars[6])]
        else:
            raise ValueError("非VSE版本号")

        Value = root.find(".//Block[@Name='Enumerated\nConstant6']").find(".//P[@Name='Value']")
        if chars[7] == '.':
            Value.text = "u8CCSoftVersNr.Char_Point"
        else:
            raise ValueError("非VSE版本号")

        Value = root.find(".//Block[@Name='Enumerated\nConstant7']").find(".//P[@Name='Value']")
        if chars[8].isdigit():
            Value.text = u8CCSoftVersNr[int(chars[8])]
        else:
            raise ValueError("非VSE版本号")

        Value = root.find(".//Block[@Name='Enumerated\nConstant8']").find(".//P[@Name='Value']")
        if chars[9].isdigit():
            Value.text = u8CCSoftVersNr[int(chars[9])]
        else:
            raise ValueError("非VSE版本号")
        tree.write(xmlPath)
    except etree.XMLSyntaxError as e:
        print(e)
        return

    os.remove(slxFile)
    DMUtils.excelUtil.zip_file(unzip_dir_path, slxFile)
    shutil.rmtree(unzip_dir_path)
    logger.debug(f"slx更新成功！")


if __name__ == '__main__':
    info = genCarIDInfo(
        r"E:\VSEAA2.0_svn\01_Development\02_SWE_SoftwareEngineeringProcessGroup\03_SWE3_SoftwareDetailedDesignAndUnitConstruction\01_ASW\D3\08_车型应用\14_HT国内升版\01_车型匹配文件\VSEAA2.0-SWE1-014_VSE2.0项目HT车型-D3车型底盘电控关键信息V1.0.0.xlsx")
    # changeGetCarIDFcn("VSE_VehDataMngt.slx",info,"vse2.30.80")
