@echo off
chcp 65001 >nul
echo ========================================
echo VSETools 便携式Python环境设置
echo ========================================

:: 设置变量
set WINPYTHON_DIR=%~dp0WinPython64-3.12.10.1dot
set PYTHON_EXE=%WINPYTHON_DIR%\python-3.12.10.amd64\python.exe
set PIP_EXE=%WINPYTHON_DIR%\python-3.12.10.amd64\Scripts\pip.exe
set VENV_DIR=%~dp0venv_vsetools

echo 1. 检查WinPython...
if not exist "%PYTHON_EXE%" (
    echo 错误: 请先将WinPython解压到当前目录
    echo 预期路径: %WINPYTHON_DIR%
    pause
    exit /b 1
)

echo 2. 创建虚拟环境...
"%PYTHON_EXE%" -m venv "%VENV_DIR%"
if errorlevel 1 (
    echo 创建虚拟环境失败
    pause
    exit /b 1
)

echo 3. 激活虚拟环境并升级pip...
call "%VENV_DIR%\Scripts\activate.bat"
python -m pip install --upgrade pip

echo 4. 安装项目依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo 安装依赖包失败
    pause
    exit /b 1
)

echo 5. 创建启动脚本...
call :create_launcher

echo ========================================
echo 环境设置完成！
echo 使用 启动VSETools.bat 运行程序
echo ========================================
pause
goto :eof

:create_launcher
echo @echo off > 启动VSETools.bat
echo chcp 65001 ^>nul >> 启动VSETools.bat
echo set VENV_DIR=%%~dp0venv_vsetools >> 启动VSETools.bat
echo call "%%VENV_DIR%%\Scripts\activate.bat" >> 启动VSETools.bat
echo cd /d "%%~dp0" >> 启动VSETools.bat
echo python main_gui.py >> 启动VSETools.bat
echo pause >> 启动VSETools.bat
goto :eof