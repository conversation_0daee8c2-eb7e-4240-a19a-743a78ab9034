@echo off
chcp 65001 >nul
echo 在WinPython中预装VSETools依赖包...

set WINPYTHON_DIR=%~dp0Winpython31210
set PYTHON_EXE=%WINPYTHON_DIR%\python\python.exe
set PIP_EXE=%WINPYTHON_DIR%\python\Scripts\pip.exe

if not exist "%PYTHON_EXE%" (
    echo 错误：未找到 %PYTHON_EXE%
    pause
    exit /b 1
)

if not exist "requirements_portable.txt" (
    echo 未找到requirements_portable.txt文件！
    pause
    exit /b 1
)

echo Python路径: %PYTHON_EXE%
echo 正在安装requirements_portable.txt中的依赖包...

:: 清除PATH中的其他Python路径，确保使用WinPython
set PATH=%WINPYTHON_DIR%\python;%WINPYTHON_DIR%\python\Scripts;%SystemRoot%\system32;%SystemRoot%

:: 使用python -m pip确保使用正确的pip
"%PYTHON_EXE%" -m pip install -r requirements_portable.txt

if errorlevel 1 (
    echo 安装失败！
    pause
    exit /b 1
)

echo 依赖包安装完成！现在可以将整个项目文件夹移动到其他电脑直接使用。
pause