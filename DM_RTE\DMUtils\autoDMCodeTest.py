import os
import shutil
import subprocess
from sys import stdout, stderr


def genDMTestCode(sourceCodePath, destinationCodePath):
    if not os.path.exists(destinationCodePath):
        os.makedirs(destinationCodePath)
    for root, dirs, files in os.walk(sourceCodePath):
       for file in files:
           if ("VSE" in file and (file.endswith(".c") or file.endswith(".h"))) or file == "rtwtypes.h":
               copyFilePath = os.path.join(destinationCodePath, file)
               if copyFilePath.endswith(".c"):
                   tmpStr = copyFilePath.split(".")[0]
                   copyFilePath = tmpStr + ".cpp"
               sourceFilePath = os.path.join(root, file)
               if os.path.exists(copyFilePath):
                   os.remove(copyFilePath)
               shutil.copy(sourceFilePath, copyFilePath)


if __name__ == '__main__':
    #genDMTestCode("E:\项目\DM生成代码\VSE_VehDataMngt_ert_rtw","E:\c学习\DM_Auto_Test\DM_Code")
    dm_project_folder=r"E:\c学习\DM_Auto_Test"
    key_car_info_filepath=r"E:\pythonProject7\VSEAA2.0-SWE1-014_VSE2.0项目HT（国内）-D3车型底盘电控关键信息V1.0.0.xlsx"
    output_test_filepath=r"E:\pythonProject7\测试结果.xlsx"


    command=os.path.join(dm_project_folder,"AutoTest.sln")
    subprocess.run(f"msbuild.exe {command}",stdout=subprocess.PIPE,stderr=subprocess.PIPE)
    exe_file_path=os.path.join(dm_project_folder,"x64\Debug\AutoTest.exe")
    subprocess.run([exe_file_path,key_car_info_filepath,output_test_filepath])

