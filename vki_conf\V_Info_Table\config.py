# config.py
import re

# 文件路径
INPUT_EXCEL_PATH = "data/底盘电控关键信息表.xlsx"
TEMPLATE_PATH = "templates/VSEAA2.0-SWE1-014_VSE2.0项目XX车型底盘电控关键信息模板.xlsx"
DATABASE_PATH = "data/my_database.db"

# 新表的前三列和数据
NEW_TABLE_COLUMNS = ["信号定义", "信号名称", "单位"]
NEW_TABLE_DATA = [
    ("ca_VSE_DM_fTireRollgRFrnt", "前轮滚动半径", "m"),
    ("ca_VSE_DM_fTireRollgRRe", "后轮滚动半径", "m"),
    ("ca_VSE_DM_fCentroidHeiFull", "等效质心高度（满载）", "m"),
    ("ca_VSE_DM_fCentroidHei", "等效质心高度（半载）", "m"),
    ("ca_VSE_DM_fCentroidLenAFull", "质心距前轴距离（满载）", "m"),
    ("ca_VSE_DM_fCentroidLenA", "质心距前轴距离（半载）", "m"),
    ("ca_VSE_DM_fFrntSuspenK1", "前悬架刚度", "N/m"),
    ("ca_VSE_DM_fCentroidLenBFull", "质心距后轴距离（满载）", "m"),
    ("ca_VSE_DM_fCentroidLenB", "质心距后轴距离（半载）", "m"),
    ("ca_VSE_DM_fReSuspenK2", "后悬架刚度", "N/m"),
    ("ca_VSE_DM_fResistPitchUpRat", "抗抬头率", "/"),
    ("ca_VSE_DM_fVehMEmpty", "总重（空载）", "kg"),
    ("ca_VSE_DM_fVehM", "总重（半载）", "kg"),
    ("ca_VSE_DM_fVehMSprung", "整车簧上质量", "kg"),
    ("ca_VSE_DM_fVehMSuspFrnt", "前悬架质量（总）", "kg"),
    ("ca_VSE_DM_fVehMSuspRe", "后悬架质量（总）", "kg"),
    ("ca_VSE_DM_fVehMTireFrnt", "前轮轮胎质量（单个）", "kg"),
    ("ca_VSE_DM_fVehMTireRe", "后轮轮胎质量（单个）", "kg"),
    ("ca_VSE_DM_fWhlWidthFrnt", "前轴轮距", "m"),
    ("ca_VSE_DM_fWhlWidthRe", "后轴轮距", "m"),
    ("ca_VSE_DM_fRollStiffRat", "DM_rollstiff_ratio侧倾杆刚度占比", "/"),
    ("ca_VSE_DM_fSteerRat", "DM_steerratio转向传动比", "/"),
    ("ca_VSE_DM_fAxleDst", "轴距", "m"),
    ("ca_VSE_DM_fFrntWhlJ", "前轮转动惯量", "kg·m2"),
    ("ca_VSE_DM_fReWhlJ", "后轮转动惯量", "kg·m2"),
    ("ca_VSE_DM_fVehJ", "整车转动惯量", "kg·m2"),
    ("ca_VSE_DM_fStabyFac", "稳定性因数", "s2/m2"),
    ("ca_VSE_DM_u8DrvMod", "车辆驱动类型", "/"),
    ("ca_VSE_DM_u8EngTyp", "引擎类型", "/"),
    ("ca_VSE_DM_u8SteerMod", "车辆转向类型", "/"),
    ("ca_VSE_DM_fKPLatOffsFrnt", "前轴主销横向偏移", "m"),
    ("ca_VSE_DM_fKPLatOffsRe", "后轴主销横向偏移", "m"),
    ("ca_VSE_DM_fBrkCylDiamFrnt", "前轴制动轮缸直径", "m"),
    ("ca_VSE_DM_fBrkCylDiamRe", "后轴制动轮缸直径", "m"),
    ("ca_VSE_DM_fBrkFricCoeffFrnt", "前轴制动摩擦系数", "/"),
    ("ca_VSE_DM_fBrkFricCoeffRe", "后轴制动摩擦系数", "/"),
    ("ca_VSE_DM_fBrkEfcRdFrnt", "前轴制动有效半径", "m"),
    ("ca_VSE_DM_fBrkEfcRdRe", "后轴制动有效半径", "m"),
    ("ca_VSE_DM_fCentSprgToCornAxis", "簧上质心与侧偏轴线距离", "m"),
    ("ca_VSE_DM_fWindage", "风阻", "/"),
    ("ca_VSE_DM_fWindwardArea", "迎风面积", "m^2"),
    ("ca_VSE_DM_fIMUAg", "IMU侧偏角度", "°"),
    ("ca_VSE_DM_fIMUPosnX", "IMU对质心X向偏移", "m"),
    ("ca_VSE_DM_fIMUPosnY", "IMU对质心Y向偏移", "m"),
    ("ca_VSE_DM_fFrntAxleR", "前轴主减速器比", "/"),
    ("ca_VSE_DM_fReAxleR", "后轴主减速器比", "/"),
    ("ca_VSE_DM_fCentSprgToPitchAxis", "簧上质心与俯仰轴线距离", "m"),
    ("ca_VSE_DM_fVehJXAxis", "车辆绕X轴转动惯量", "kg·m2"),
    ("ca_VSE_DM_fVehJYAxis", "车辆绕Y轴转动惯量", "kg·m2"),
    ("ca_VSE_DM_fSuspLeverRatFrnt", "前悬架杠杆比", "/"),
    ("ca_VSE_DM_fSuspLeverRatRe", "后悬架杠杆比", "/"),
    ("ca_VSE_DM_fDamprBalPosnFL", "左前减振器平衡位置", "mm"),
    ("ca_VSE_DM_fDamprBalPosnFR", "右前减振器平衡位置", "mm"),
    ("ca_VSE_DM_fDamprBalPosnRL", "左后减振器平衡位置", "mm"),
    ("ca_VSE_DM_fDamprBalPosnRR", "右后减振器平衡位置", "mm"),
    ("ca_VSE_DM_fFL_Posn_X", "ECU安装位置与左前减振器塔顶纵向距离", "[m]"),
    ("ca_VSE_DM_fFL_Posn_Y", "ECU安装位置与左前减振器塔顶横向距离", "[m]"),
    ("ca_VSE_DM_fFR_Posn_X", "ECU安装位置与右前减振器塔顶纵向距离", "[m]"),
    ("ca_VSE_DM_fFR_Posn_Y", "ECU安装位置与右前减振器塔顶横向距离", "[m]"),
    ("ca_VSE_DM_fRL_Posn_X", "ECU安装位置与左后减振器塔顶纵向距离", "[m]"),
    ("ca_VSE_DM_fRL_Posn_Y", "ECU安装位置与左后减振器塔顶横向距离", "[m]"),
    ("ca_VSE_DM_fRR_Posn_X", "ECU安装位置与右后减振器塔顶纵向距离", "[m]"),
    ("ca_VSE_DM_fRR_Posn_Y", "ECU安装位置与右后减振器塔顶横向距离", "[m]"),
    ("ca_VSE_DM_fAgtoHeit_FL", "左前角度高度转换系数", "[/]"),
    ("ca_VSE_DM_fAgtoHeit_FR", "右前角度高度转换系数", "[/]"),
    ("ca_VSE_DM_fAgtoHeit_RL", "左后角度高度转换系数", "[/]"),
    ("ca_VSE_DM_fAgtoHeit_RR", "右后角度高度转换系数", "[/]"),
    ("ca_VSE_DM_fDamprCoeff", "减振器阻尼系数", "N·s/m"),
    ("ca_VSE_DM_u8SuspMod", "悬架类型", "/"),
    ("ca_VSE_DM_fSteerFrntAg", "方向盘与前轮转角", "°"),
    ("ca_VSE_DM_fWhlSteerAgInFrnt", "内侧车轮转角(前)", "°"),
    ("ca_VSE_DM_fWhlSteerAgOutFrnt", "外侧车轮转角(前)", "°"),
]

# 对应关系和单位转换
MAPPING = {
    "ca_VSE_DM_fTireRollgRFrnt": {"描述2": "Dynamic tire radius front axle/前轴车轮滚动半径",
                                  "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fTireRollgRRe": {"描述2": "Dynamic tire radius rear axle/后轴车轮滚动半径",
                                "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fCentroidHeiFull": {"描述2": "满载时质心高度", "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fCentroidHei": {"描述2": "半载时质心高度", "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fCentroidLenAFull": {"描述2": "满载时质心与前轴距离", "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fCentroidLenA": {"描述2": "半载时质心与前轴距离", "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fFrntSuspenK1": {"描述2": "前悬架刚度", "单位转换": lambda x: x * 1000},
    "ca_VSE_DM_fCentroidLenBFull": {"特殊处理": True},
    "ca_VSE_DM_fCentroidLenB": {"特殊处理": True},
    "ca_VSE_DM_fReSuspenK2": {"描述2": "后悬架刚度", "单位转换": lambda x: x * 1000},
    "ca_VSE_DM_fResistPitchUpRat": {"描述2": "抗抬头率"},
    "ca_VSE_DM_fVehMEmpty": {"描述2": "整备", "描述3": "Total 总重"},
    "ca_VSE_DM_fVehM": {"描述2": "半载", "描述3": "Total 总重"},
    "ca_VSE_DM_fVehMSprung": {"描述2": "整车簧上质量"},
    "ca_VSE_DM_fVehMSuspFrnt": {"描述2": "前悬架质量（总）"},
    "ca_VSE_DM_fVehMSuspRe": {"描述2": "后悬架质量（总）"},
    "ca_VSE_DM_fVehMTireFrnt": {"描述2": "前轮质量（单个）"},
    "ca_VSE_DM_fVehMTireRe": {"描述2": "后轮质量（单个）"},
    "ca_VSE_DM_fWhlWidthFrnt": {"描述2": "前轮距", "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fWhlWidthRe": {"描述2": "后轮距", "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fRollStiffRat": {"描述2": "稳定杆侧倾刚度占比"},
    "ca_VSE_DM_fSteerRat": {"描述1": "转向系统", "描述2": "转向角传动比（从方向盘输入到转向轮输出）"},
    "ca_VSE_DM_fAxleDst": {"描述2": "轴距", "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fFrntWhlJ": {"描述2": "前轮转动惯量"},
    "ca_VSE_DM_fReWhlJ": {"描述2": "后轮转动惯量"},
    "ca_VSE_DM_fVehJ": {"描述2": "半载", "描述3": "转动惯量"},
    "ca_VSE_DM_fStabyFac": {"默认值": 0.001},
    "ca_VSE_DM_u8DrvMod": {"描述2": "驱动方式", "特殊处理": lambda x: {"前驱": 0, "后驱": 1, "四驱": 2}.get(x, x)},
    "ca_VSE_DM_u8EngTyp": {"描述2": "引擎类型",
                           "特殊处理": lambda x: {"前驱单电机": 0, "后驱单电机": 1, "四驱前后单电机": 2,
                                                  "四驱前后双电机": 3, "四驱前单电机后双电机": 4,
                                                  "四驱前双电机后单电机": 5}.get(x, x)},
    "ca_VSE_DM_u8SteerMod": {"描述2": "车辆转向类型", "特殊处理": lambda x: {"前轮转向": 0, "四轮转向": 1}.get(x, x)},
    "ca_VSE_DM_fKPLatOffsFrnt": {"默认值": 0},
    "ca_VSE_DM_fKPLatOffsRe": {"默认值": 0},
    "ca_VSE_DM_fBrkCylDiamFrnt": {"描述2": "前制动缸直径", "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fBrkCylDiamRe": {"描述2": "后制动缸直径", "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fBrkFricCoeffFrnt": {"描述2": "前制动缸摩擦系数"},
    "ca_VSE_DM_fBrkFricCoeffRe": {"描述2": "后制动缸摩擦系数"},
    "ca_VSE_DM_fBrkEfcRdFrnt": {"描述2": "前制动缸有效制动半径"},
    "ca_VSE_DM_fBrkEfcRdRe": {"描述2": "后制动缸有效制动半径"},
    "ca_VSE_DM_fCentSprgToCornAxis": {"描述2": "簧上质心与侧偏轴线距离"},
    "ca_VSE_DM_fWindage": {"描述2": "风阻系数"},
    "ca_VSE_DM_fWindwardArea": {"描述2": "迎风面积"},
    "ca_VSE_DM_fIMUAg": {"描述2": "IMU侧偏角度"},
    "ca_VSE_DM_fIMUPosnX": {"描述2": "IMU安装位置与质心位置纵向距离", "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fIMUPosnY": {"描述2": "IMU安装位置与质心位置横向距离", "单位转换": lambda x: x / 1000},
    "ca_VSE_DM_fFrntAxleR": {"默认值": 1},
    "ca_VSE_DM_fReAxleR": {"默认值": 1},
    "ca_VSE_DM_fCentSprgToPitchAxis": {"描述2": "簧上质心与俯仰轴线距离"},
    "ca_VSE_DM_fVehJXAxis": {"描述2": "车辆绕X轴转动惯量"},
    "ca_VSE_DM_fVehJYAxis": {"描述2": "车辆绕Y轴转动惯量"},
    "ca_VSE_DM_fSuspLeverRatFrnt": {"描述2": "前弹簧杠杆比"},
    "ca_VSE_DM_fSuspLeverRatRe": {"描述2": "后弹簧杠杆比"},
    "ca_VSE_DM_fDamprBalPosnFL": {"默认值": 0},
    "ca_VSE_DM_fDamprBalPosnFR": {"默认值": 0},
    "ca_VSE_DM_fDamprBalPosnRL": {"默认值": 0},
    "ca_VSE_DM_fDamprBalPosnRR": {"默认值": 0},
    "ca_VSE_DM_fFL_Posn_X": {"描述2": "与左前减振器塔顶距离 mm",
                             "特殊处理": lambda x: re.search(r'[xX][：:](\S+)', x).group(1) if re.search(r'[xX][：:]', x) else None},
    "ca_VSE_DM_fFL_Posn_Y": {"描述2": "与左前减振器塔顶距离 mm",
                             "特殊处理": lambda x: re.search(r'[yY][：:](\S+)', x).group(1) if re.search(r'[yY][：:]', x) else None},
    "ca_VSE_DM_fFR_Posn_X": {"描述2": "与右前减振器塔顶距离 mm",
                             "特殊处理": lambda x: re.search(r'[xX][：:](\S+)', x).group(1) if re.search(r'[xX][：:]', x) else None},
    "ca_VSE_DM_fFR_Posn_Y": {"描述2": "与右前减振器塔顶距离 mm",
                             "特殊处理": lambda x: re.search(r'[yY][：:](\S+)', x).group(1) if re.search(r'[yY][：:]', x) else None},
    "ca_VSE_DM_fRL_Posn_X": {"描述2": "与左后减振器塔顶距离 mm",
                             "特殊处理": lambda x: re.search(r'[xX][：:](\S+)', x).group(1) if re.search(r'[xX][：:]', x) else None},
    "ca_VSE_DM_fRL_Posn_Y": {"描述2": "与左后减振器塔顶距离 mm",
                             "特殊处理": lambda x: re.search(r'[yY][：:](\S+)', x).group(1) if re.search(r'[yY][：:]', x) else None},
    "ca_VSE_DM_fRR_Posn_X": {"描述2": "与右后减振器塔顶距离 mm",
                             "特殊处理": lambda x: re.search(r'[xX][：:](\S+)', x).group(1) if re.search(r'[xX][：:]', x) else None},
    "ca_VSE_DM_fRR_Posn_Y": {"描述2": "与右后减振器塔顶距离 mm",
                             "特殊处理": lambda x: re.search(r'[yY][：:](\S+)', x).group(1) if re.search(r'[yY][：:]', x) else None},
    "ca_VSE_DM_fAgtoHeit_FL": {"默认值": 2.9482},
    "ca_VSE_DM_fAgtoHeit_FR": {"默认值": -2.9579},
    "ca_VSE_DM_fAgtoHeit_RL": {"默认值": 2.7547},
    "ca_VSE_DM_fAgtoHeit_RR": {"默认值": -2.7513},
    "ca_VSE_DM_fDamprCoeff": {"默认值": 0},
    "ca_VSE_DM_u8SuspMod": {},
    "ca_VSE_DM_fSteerFrntAg": {"描述1": "转向系统", "描述2": "方向盘转角"},
    "ca_VSE_DM_fWhlSteerAgInFrnt": {"描述1": "转向系统", "描述2": "内侧轮转角(前） °"},
    "ca_VSE_DM_fWhlSteerAgOutFrnt": {"描述1": "转向系统", "描述2": "外侧轮转角(前） °"},
}