import os
import shutil
import subprocess
import fileinput
import sys

def copy_directory(src, dst):
    """Copy directory contents from src to dst, overwriting existing files."""
    if os.path.exists(dst):
        shutil.rmtree(dst)
    shutil.copytree(src, dst)
    print(f"已复制 {src} 到 {dst}")

def copy_files(src, dst):
    """Copy files from src to dst, overwriting existing files."""
    if not os.path.exists(dst):
        os.makedirs(dst)
    for item in os.listdir(src):
        src_path = os.path.join(src, item)
        dst_path = os.path.join(dst, item)
        if os.path.isfile(src_path):
            shutil.copy2(src_path, dst_path)
            print(f"已复制 {src_path} 到 {dst_path}")

def move_files(src, dst):
    """Move all files from src to dst."""
    if not os.path.exists(dst):
        os.makedirs(dst)
    for item in os.listdir(src):
        src_path = os.path.join(src, item)
        dst_path = os.path.join(dst, item)
        if os.path.isfile(src_path):
            shutil.move(src_path, dst_path)
            print(f"已移动 {src_path} 到 {dst_path}")

def clear_directory(path):
    """Clear all contents of the specified directory if it exists."""
    if os.path.exists(path):
        shutil.rmtree(path)
        os.makedirs(path)
        print(f"已清空 {path}")

def update_config_file(config_path, new_filename):
    """Update INPUT_EXCEL_PATH in V_Info_Table/config.py with new filename."""
    with fileinput.FileInput(config_path, inplace=True, encoding='utf-8') as file:
        for line in file:
            if line.strip().startswith("INPUT_EXCEL_PATH"):
                print(f'INPUT_EXCEL_PATH = "data/{new_filename}"')
            else:
                print(line, end='')

def run_v_info_table():
    """Handle V_Info_Table project: copy input, update config, run, copy output."""
    # Create input folder if it doesn't exist
    input_dir = "input"
    if not os.path.exists(input_dir):
        print("错误: input 文件夹不存在")
        return

    # Create V_Info_Table/data if it doesn't exist
    data_dir = "V_Info_Table/data"
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    # Copy files containing "关键信息" from input to V_Info_Table/data
    info_files = [f for f in os.listdir(input_dir) if "关键信息" in f]
    if not info_files:
        print("错误: input 文件夹中没有包含‘关键信息’的文件")
        return

    for filename in info_files:
        src_path = os.path.join(input_dir, filename)
        dst_path = os.path.join(data_dir, filename)
        shutil.copy2(src_path, dst_path)
        print(f"已复制 {src_path} 到 {dst_path}")

        # Update config with current filename
        config_path = os.path.join("V_Info_Table", "config.py")
        if os.path.exists(config_path):
            update_config_file(config_path, filename)

        # Verify if the input file exists
        input_file_path = os.path.join(data_dir, filename)
        if not os.path.exists(input_file_path):
            print(f"错误: {input_file_path} 不存在")
            continue

        # Run V_Info_Table main.py with correct working directory
        print(f"运行 V_Info_Table/main.py，处理文件: {filename}，工作目录: {os.path.abspath('V_Info_Table')}")
        try:
            subprocess.run([sys.executable, "main.py"], cwd="V_Info_Table", check=True)
        except subprocess.CalledProcessError as e:
            print(f"运行 V_Info_Table/main.py 失败: {e}")
            continue

        # Copy output from V_Info_Table/outputs to root
        if os.path.exists("V_Info_Table/outputs"):
            copy_files("V_Info_Table/outputs", ".")

def run_v_conf_table():
    """Handle V_Conf_Table project: copy input, run, move output."""
    # Create input folder if it doesn't exist
    input_dir = "input"
    if not os.path.exists(input_dir):
        print("错误: input 文件夹不存在")
        return

    # Create V_Conf_Table/data/input
    input_dst = "V_Conf_Table/data/input"
    if not os.path.exists(input_dst):
        os.makedirs(input_dst)

    # Copy files containing "配置字" from input to V_Conf_Table/data/input
    conf_files = [f for f in os.listdir(input_dir) if "配置字" in f]
    if not conf_files:
        print("错误: input 文件夹中没有包含‘配置字’的文件")
        return

    for filename in conf_files:
        src_path = os.path.join(input_dir, filename)
        dst_path = os.path.join(input_dst, filename)
        shutil.copy2(src_path, dst_path)
        print(f"已复制 {src_path} 到 {dst_path}")

    # Run V_Conf_Table main.py with correct working directory
    print(f"运行 V_Conf_Table/src/main.py，工作目录: {os.path.abspath('V_Conf_Table/src')}")
    try:
        subprocess.run([sys.executable, "main.py"], cwd="V_Conf_Table/src", check=True)
    except subprocess.CalledProcessError as e:
        print(f"运行 V_Conf_Table/src/main.py 失败: {e}")
        return

    # Move files from V_Conf_Table/data/template_output to root
    template_output_dst = "V_Conf_Table/data/template_output"
    if os.path.exists(template_output_dst):
        move_files(template_output_dst, ".")

def clear_directories():
    """Clear specified directories after execution."""
    directories = [
        "input",
        "V_Info_Table/data",
        "V_Info_Table/outputs",
        "V_Conf_Table/data/input",
        "V_Conf_Table/data/template_output",
        "V_Conf_Table/data/output"
    ]
    for path in directories:
        clear_directory(path)

def main():
    """Main function to run selected projects based on user input."""
    choice = input("请输入运行选项（1: 关键信息表, 2: 配置字表, 3: 两者）：")

    try:
        if choice == "1":
            run_v_info_table()
        elif choice == "2":
            run_v_conf_table()
        elif choice == "3":
            run_v_conf_table()
            run_v_info_table()
        else:
            print("无效输入，请输入 1, 2 或 3")
            return
    finally:
        # Clear specified directories after execution
        clear_directories()

if __name__ == "__main__":
    main()