from excel_processor import ExcelProcessor
from config import ensure_directories
from utils import setup_logging

logger = setup_logging()

def main():
    """主程序入口"""
    try:
        # 确保目录存在
        ensure_directories()

        # 输入参数（可改为UI输入）
        model = input("请输入车型代号（如HC2）：").strip()
        controller = input("请输入控制器名称（如D2、域控）：").strip()

        if not model or not controller:
            raise ValueError("车型代号和控制器名称不能为空")

        logger.info(f"开始处理：车型={model}, 控制器={controller}")

        # 创建处理器并运行
        processor = ExcelProcessor(model, controller)
        processor.run()

        logger.info("程序执行完成")
    except Exception as e:
        logger.error(f"程序运行失败：{str(e)}")
        raise

if __name__ == "__main__":
    main()