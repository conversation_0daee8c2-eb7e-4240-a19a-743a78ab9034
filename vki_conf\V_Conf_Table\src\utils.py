import logging
import re
from pathlib import Path
from typing import List, Union
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.styles import Font, PatternFill

from config import LOG_FILE

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.INFO,  # 可临时改为logging.DEBUG
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(LOG_FILE, encoding="utf-8"),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()  # 在函数定义之后初始化logger

def is_valid_sheet_name(sheet_name: str, rule: any) -> bool:
    """检查Sheet名称是否符合规则"""
    if isinstance(rule, str):
        return bool(re.match(rule, sheet_name))
    elif isinstance(rule, list):
        return sheet_name in rule
    elif callable(rule):
        return rule(sheet_name)
    return False

def is_strikethrough(cell) -> bool:
    """检查单元格文本是否被划掉"""
    if cell.font and cell.font.strike:
        return True
    return False

def get_merged_cell_value(ws: Worksheet, row: int, col: int) -> str:
    """获取合并单元格的值"""
    cell = ws.cell(row=row, column=col)
    for merged_range in ws.merged_cells.ranges:
        if cell.coordinate in merged_range:
            return ws[merged_range.start_cell.coordinate].value or ""
    return cell.value or ""

def apply_yellow_fill(cell):
    """为单元格应用黄色填充"""
    yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
    cell.fill = yellow_fill

def copy_cell_format(source_cell, target_cell):
    """复制源单元格的格式到目标单元格"""
    try:
        value = source_cell.value
        clean_value = str(value).strip() if value is not None else ""
        logger.debug(
            f"复制单元格格式：coordinate={source_cell.coordinate}, "
            f"值='{clean_value}', 长度={len(clean_value)}"
        )

        # 仅复制字体样式（简化）
        if source_cell.font:
            strike = source_cell.font.strike if source_cell.font.strike is not None else False
            logger.debug(f"复制单元格格式：strike={strike}")
            target_cell.font = Font(
                name=source_cell.font.name or "Microsoft YaHei",
                size=source_cell.font.size or 12,
                strike=strike
            )
        else:
            target_cell.font = Font(name="Microsoft YaHei", size=12, strike=False)
            logger.debug("源单元格无字体样式，使用默认格式")
    except Exception as e:
        logger.error(
            f"复制单元格格式失败，coordinate={source_cell.coordinate}, "
            f"值='{clean_value}', 错误：{str(e)}"
        )
        target_cell.font = Font(name="Microsoft YaHei", size=12, strike=False)