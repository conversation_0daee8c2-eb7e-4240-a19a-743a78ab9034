@echo off
chcp 65001 >nul
echo 正在启动VSETools...

:: 设置项目根目录
set PROJECT_DIR=%~dp0
cd /d "%PROJECT_DIR%"

:: 方案1：检查WinPython 3.12（推荐）
set WINPYTHON_DIR=%PROJECT_DIR%Winpython31210
set WINPYTHON_EXE=%WINPYTHON_DIR%\python\python.exe

:: 方案1.1：检查WinPython 3.8（用于AutoCali）
set WINPYTHON38_DIR=%PROJECT_DIR%Winpython30810
set WINPYTHON38_EXE=%WINPYTHON38_DIR%\python-3.8.10.amd64\python.exe

if exist "%WINPYTHON_EXE%" (
    echo 使用WinPython 3.12环境...
    set PYTHON_EXE=%WINPYTHON_EXE%
    set PYTHON38_EXE=%WINPYTHON38_EXE%
    set PYTHONPATH=%PROJECT_DIR%;%%PYTHONPATH%%
    goto :run_app
)

:: 方案2：检查虚拟环境
set VENV_DIR=%PROJECT_DIR%venv_vsetools
set VENV_PYTHON=%VENV_DIR%\Scripts\python.exe

if exist "%VENV_PYTHON%" (
    echo 使用虚拟环境...
    call "%VENV_DIR%\Scripts\activate.bat" >nul 2>&1
    set PYTHON_EXE=%VENV_PYTHON%
    set PYTHONPATH=%PROJECT_DIR%;%%PYTHONPATH%%
    goto :run_app
)

:: 方案3：使用系统Python
echo 未找到便携式Python环境，尝试使用系统Python...
set PYTHON_EXE=python
set PYTHONPATH=%PROJECT_DIR%;%%PYTHONPATH%%

:run_app
echo 启动应用程序...
echo Python 3.12: %PYTHON_EXE%
if exist "%PYTHON38_EXE%" echo Python 3.8: %PYTHON38_EXE%

:: 设置环境变量，传递Python路径给主程序
set VSETOOLS_PYTHON312=%PYTHON_EXE%
if exist "%PYTHON38_EXE%" set VSETOOLS_PYTHON38=%PYTHON38_EXE%

"%PYTHON_EXE%" main_gui.py
if errorlevel 1 (
    echo.
    echo 启动失败！请检查 Winpython31210 文件夹是否存在
    pause
)
exit /b %errorlevel%