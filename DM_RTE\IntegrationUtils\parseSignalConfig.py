import json

def genSignalFigureFile(figure_signals,figureHeadFile,figureSourceFile):

    with open(figureSourceFile, 'r',encoding='utf-8') as file:
        source_code=file.read()

    source_code=(str.split(source_code,"//添加配置"))[0]

    tmp_str="//添加配置\n"
    tmp_str+="std::vector<std::string> sig_dict = {"
    pic_names = figure_signals.keys()
    for i in pic_names:
        tmp_str+=f"\"{i}\","
    tmp_str+="};\n"

    for i in pic_names:
        tmp_str+=f"std::map<std::string,std::vector<float>>{i}=genSignalMap("
        tmp_str+="{"
        for j in figure_signals[i]:
            tmp_str+=f"\"{j}\","
        tmp_str+="});\n"

    tmp_str += "std::vector<std::map<std::string,std::vector<float>>*>plot_map={"
    for i in pic_names:
        tmp_str+=f"&{i},"
    tmp_str+="};\n"
    source_code+=tmp_str

    with open(figureSourceFile, "w", encoding='utf-8') as file:
        file.write(source_code)

    with open(figureHeadFile,'r',encoding="utf-8") as file:
        head_code=file.read()
    tmp_str="//修改代码\n"
    head_code=(str.split(head_code,"//修改代码"))[0]
    tmp_str+="extern std::vector<std::string> sig_dict;\n"
    for i in pic_names:
        tmp_str+=f"extern std::map<std::string, std::vector<float>>{i};\n"
    tmp_str+="extern std::vector<std::map<std::string, std::vector<float>>*>plot_map;\n#endif"
    head_code+=tmp_str

    with open(figureHeadFile,'w',encoding='utf-8') as file:
        file.write(head_code)



