由于dms网站有更新，现在找不到评审人方案的输入框或者该输入框的下拉按钮，点击到这个框或下拉按钮，才可出现一些评审人方案，然后从中选择需要的方案。

fn点击下面这个容器里的可以点击的元素应该都是可以的（人能感受到的可以点击的就是一个输入框或输入框右侧的下拉按钮，当然程序是不是能点击更多的就不知道了），成功点击其中一个即可。最外层容器的xpath为： //*[@id="el-collapse-content-45"]/div/form/div[1]/div
<div class="el-form-item__content">
    <div class="el-select">
        <div class="el-select__wrapper is-filterable el-tooltip__trigger el-tooltip__trigger" tabindex="-1"><!--v-if-->
            <div class="el-select__selection"><!--v-if-->
                <div class="el-select__selected-item el-select__input-wrapper"><input type="text"
                        class="el-select__input" autocomplete="off" tabindex="0" role="combobox" spellcheck="false"
                        aria-activedescendant="el-id-3532-62" aria-controls="el-id-3532-47" aria-expanded="false"
                        aria-autocomplete="none" aria-haspopup="listbox" id="el-id-3532-60"
                        style="min-width: 11px;"><span aria-hidden="true" class="el-select__input-calculator"></span>
                </div>
                <div class="el-select__selected-item el-select__placeholder"><span>数据管理员、科长、项目主管</span></div>
            </div>
            <div class="el-select__suffix"><i class="el-icon el-select__caret el-select__icon"><svg
                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                        <path fill="currentColor"
                            d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z">
                        </path>
                    </svg></i><!--v-if--><!--v-if--></div>
        </div>
    </div>
</div>

dvp点击下面这个容器里的可以点击的元素应该都是可以的，成功点击其中一个即可。最外层容器的xpath为：//*[@id="el-collapse-content-107"]/div/form/div[1]/div
<div class="el-form-item__content">
    <div class="el-select">
        <div class="el-select__wrapper is-filterable el-tooltip__trigger el-tooltip__trigger" tabindex="-1"><!--v-if-->
            <div class="el-select__selection"><!--v-if-->
                <div class="el-select__selected-item el-select__input-wrapper"><input type="text"
                        class="el-select__input" autocomplete="off" tabindex="0" role="combobox" spellcheck="false"
                        aria-activedescendant="" aria-controls="el-id-6813-109" aria-expanded="false"
                        aria-autocomplete="none" aria-haspopup="listbox" id="el-id-6813-125"
                        style="min-width: 11px;"><span aria-hidden="true" class="el-select__input-calculator"></span>
                </div>
                <div class="el-select__selected-item el-select__placeholder">
                    <span>数据管理员、部门相关方、系统专家、项目主管、车型品质主管、车型研发品质经理</span></div>
            </div>
            <div class="el-select__suffix"><i class="el-icon el-select__caret el-select__icon"><svg
                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                        <path fill="currentColor"
                            d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z">
                        </path>
                    </svg></i><!--v-if--><!--v-if--></div>
        </div>
    </div>
</div>

ppl点击下面这个容器里的可以点击的元素应该都是可以的，成功点击其中一个即可。最外层容器的xpath为：//*[@id="el-collapse-content-107"]/div/form/div[1]/div
<div class="el-form-item__content">
    <div class="el-select">
        <div class="el-select__wrapper is-filterable el-tooltip__trigger el-tooltip__trigger" tabindex="-1"><!--v-if-->
            <div class="el-select__selection"><!--v-if-->
                <div class="el-select__selected-item el-select__input-wrapper"><input type="text"
                        class="el-select__input" autocomplete="off" tabindex="0" role="combobox" spellcheck="false"
                        aria-activedescendant="" aria-controls="el-id-1911-109" aria-expanded="false"
                        aria-autocomplete="none" aria-haspopup="listbox" id="el-id-1911-125"
                        style="min-width: 11px;"><span aria-hidden="true" class="el-select__input-calculator"></span>
                </div>
                <div class="el-select__selected-item el-select__placeholder"><span>数据管理员、科长、相关方</span></div>
            </div>
            <div class="el-select__suffix"><i class="el-icon el-select__caret el-select__icon"><svg
                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                        <path fill="currentColor"
                            d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z">
                        </path>
                    </svg></i><!--v-if--><!--v-if--></div>
        </div>
    </div>
</div>