@echo off
chcp 65001 >nul
echo ========================================
echo VSETools 1.0 快捷方式创建工具
echo ========================================

set "SCRIPT_DIR=%~dp0"
set "SHORTCUT_NAME=VSETools1.0"
set "ICON_FILE=%SCRIPT_DIR%bV.ico"

echo 当前目录: %SCRIPT_DIR%

:: 检查必要的文件是否存在
if not exist "%SCRIPT_DIR%启动VSETools.vbs" (
    echo 错误: 找不到 启动VSETools.vbs 文件
    pause
    exit /b 1
)

:: 检查图标文件是否存在，如果不存在则使用默认图标
if not exist "%ICON_FILE%" (
    echo 警告: 找不到图标文件 bV.ico，将使用默认图标
    set "ICON_LOCATION=shell32.dll, 3"
) else (
    set "ICON_LOCATION=%ICON_FILE%"
)

:: 使用Windows Script Host创建快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(oWS.SpecialFolders("Desktop") ^& "\%SHORTCUT_NAME%.lnk") >> CreateShortcut.vbs
echo oLink.TargetPath = "%SCRIPT_DIR%启动VSETools.vbs" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%SCRIPT_DIR%" >> CreateShortcut.vbs
echo oLink.Description = "VSETools 1.0" >> CreateShortcut.vbs
echo oLink.IconLocation = "%ICON_LOCATION%" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

cscript //nologo CreateShortcut.vbs

del CreateShortcut.vbs

echo.
echo 已在桌面创建 "%SHORTCUT_NAME%.lnk" 快捷方式
echo.
echo 请将整个分发包文件夹复制到目标电脑后，
echo 在目标电脑上再次运行此脚本以创建新的快捷方式。
echo.
pause