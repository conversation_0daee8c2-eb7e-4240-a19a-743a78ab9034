@echo off
chcp 65001 >nul
title VSETools 1.0 便携版

:: 设置环境变量
set VENV_DIR=%~dp0venv_vsetools
set PYTHON_EXE=%VENV_DIR%\Scripts\python.exe

echo ========================================
echo VSETools 1.0 便携版启动
echo ========================================

:: 检查虚拟环境是否存在
if not exist "%VENV_DIR%" (
    echo 错误: 虚拟环境不存在
    echo 请先运行 setup_portable_env.bat 设置环境
    pause
    exit /b 1
)

:: 检查Python可执行文件
if not exist "%PYTHON_EXE%" (
    echo 错误: Python解释器不存在
    echo 路径: %PYTHON_EXE%
    pause
    exit /b 1
)

:: 激活虚拟环境
echo 激活Python虚拟环境...
call "%VENV_DIR%\Scripts\activate.bat"

:: 切换到项目目录
cd /d "%~dp0"

:: 设置Python路径
set PYTHONPATH=%~dp0;%PYTHONPATH%

:: 启动程序
echo 启动VSETools...
echo.
python main_gui.py

:: 如果程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo 程序异常退出，错误代码: %errorlevel%
    echo 请检查错误信息或运行 check_env.py 检查环境
)

echo.
echo 程序已退出
pause