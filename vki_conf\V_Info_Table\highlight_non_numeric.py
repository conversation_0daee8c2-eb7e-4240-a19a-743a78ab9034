# highlight_non_numeric.py
import pandas as pd
import sqlite3
import re
import os
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
from config import DATABASE_PATH, NEW_TABLE_DATA
from utils import is_numeric_only

def highlight_non_numeric_in_vehicle_columns(output_excel_path, db_path=DATABASE_PATH):
    """高亮车型列中的非纯数字值和超出范围的参数值"""
    # 检查输出文件是否存在
    if not os.path.exists(output_excel_path):
        raise FileNotFoundError(f"输出文件未找到: {output_excel_path}")

    # 读取 new_vehicle_table 以获取车型列名称
    conn = sqlite3.connect(db_path)
    new_df = pd.read_sql_query("SELECT * FROM new_vehicle_table", conn)
    conn.close()

    # 获取车型列名称
    vehicle_column_names = new_df.columns[3:].tolist()
    print(f"车型列名称: {vehicle_column_names}")

    # 读取输出 Excel 文件
    wb = load_workbook(output_excel_path)
    if "整车参数" not in wb.sheetnames:
        raise ValueError("输出文件中未找到 '整车参数' sheet")
    ws = wb["整车参数"]

    # 读取表头
    headers = [cell.value for cell in ws[1]]
    if not headers:
        raise ValueError("表头为空，无法识别列")

    # 确定车型列的索引
    vehicle_columns = [i + 1 for i, header in enumerate(headers) if header in vehicle_column_names]
    print(f"识别到的车型列索引: {vehicle_columns}")
    print(f"对应的表头: {[headers[i-1] for i in vehicle_columns]}")

    # 定义黄色填充
    yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

    # 获取单位为 m 的参数
    meter_params = [row[0] for row in NEW_TABLE_DATA if row[2] == "m"]

    # 定义范围检查规则
    range_checks = {
        "ca_VSE_DM_fFrntWhlJ": lambda x: x > 10,  # 前轮转动惯量
        "ca_VSE_DM_fReWhlJ": lambda x: x > 10,    # 后轮转动惯量
        "ca_VSE_DM_fVehJ": lambda x: x > 50000,   # 整车转动惯量
        "ca_VSE_DM_fFrntSuspenK1": lambda x: x < 1000,  # 前悬架刚度
        "ca_VSE_DM_fReSuspenK2": lambda x: x < 1000,    # 后悬架刚度
    }

    # 第一步：高亮非数字值
    for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
        for col_idx in vehicle_columns:
            cell = row[col_idx - 1]
            value = cell.value
            if not is_numeric_only(value):
                cell.fill = yellow_fill
                print(f"标黄单元格 (非数字): {cell.coordinate}, 值: {value}")

    # 第二步：检查参数范围并标黄
    signal_col_idx = headers.index("信号名") + 1  # 假设“信号名”是“信号定义”列
    for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
        signal_def = row[signal_col_idx - 1].value  # 获取信号定义
        if not signal_def:
            continue

        # 检查特定参数的范围
        check_func = range_checks.get(signal_def)
        is_meter_param = signal_def in meter_params

        for col_idx in vehicle_columns:
            cell = row[col_idx - 1]
            value = cell.value

            # 跳过非数字值（已标黄）
            if not is_numeric_only(value):
                continue

            try:
                # 尝试解析值（可能是单个数值或数组）
                if isinstance(value, str) and value.startswith("[") and value.endswith("]"):
                    # 数组值：检查每个元素
                    values = [float(x) for x in value[1:-1].split()]
                    if is_meter_param and any(v > 10 for v in values):
                        cell.fill = yellow_fill
                        print(f"标黄单元格 (单位m超出范围): {cell.coordinate}, 值: {value}")
                    elif check_func and any(check_func(v) for v in values):
                        cell.fill = yellow_fill
                        print(f"标黄单元格 (参数范围异常): {cell.coordinate}, 值: {value}")
                else:
                    # 单个数值
                    value = float(value)
                    if is_meter_param and value > 10:
                        cell.fill = yellow_fill
                        print(f"标黄单元格 (单位m超出范围): {cell.coordinate}, 值: {value}")
                    elif check_func and check_func(value):
                        cell.fill = yellow_fill
                        print(f"标黄单元格 (参数范围异常): {cell.coordinate}, 值: {value}")
            except (ValueError, TypeError):
                # 非数字值已在上一步标黄，这里跳过
                continue

    # 保存文件
    wb.save(output_excel_path)
    print(f"已完成对 {output_excel_path} 的处理，标黄非纯数字及超出范围的车型列单元格")

        
    # 在第一行前插入一个空行，并在第4个单元格填入"车型"
    wb = load_workbook(output_excel_path)
    if "整车参数" not in wb.sheetnames:
        raise ValueError("输出文件中未找到 '整车参数' sheet")
    ws = wb["整车参数"]
    
    # 插入新行
    ws.insert_rows(1)
    
    # 在第5个单元格填入"车型"
    cell = ws.cell(row=1, column=5)
    cell.value = "车型"
    
    # 设置字体为宋体12号
    from openpyxl.styles import Font
    cell.font = Font(name="宋体", size=12)
    
    # 保存文件
    wb.save(output_excel_path)
    print("已在第一行前插入空行，并在第5个单元格填入'车型'字样")

if __name__ == "__main__":
    # 示例调用
    output_excel_path = "outputs/HTEBU-VKI-CHC-10001_output.xlsx"
    highlight_non_numeric_in_vehicle_columns(output_excel_path)