2025-08-22 15:34:03,913 - INFO - 📋 开始处理第二个页面，文件类型: PPL
2025-08-22 15:34:03,913 - INFO - 📄 处理文件: MYHB_PPL_A19-100005-MY项目VSE软件开发匹配测试计划.xlsx
2025-08-22 15:34:03,913 - INFO - 🔄 检查标签页情况...
2025-08-22 15:34:03,917 - INFO - 当前有 1 个标签页
2025-08-22 15:34:03,917 - INFO - ✅ 第二个页面在当前标签页，无需切换
2025-08-22 15:34:06,918 - INFO - ⏳ 等待第二个页面加载...
2025-08-22 15:34:06,931 - INFO - ✅ 方法1: 找到最外层容器
2025-08-22 15:34:10,932 - INFO - 📜 滚动到评审人信息区域...
2025-08-22 15:34:10,962 - WARNING - ⚠️ 方法1: 所有容器ID都失败
2025-08-22 15:34:13,183 - INFO - ✅ 方法3: 通过表单区域滚动成功
2025-08-22 15:34:15,184 - INFO - 📊 读取Excel数据...
2025-08-22 15:34:15,185 - INFO - 📄 使用Excel文件: Fill_Template_Data.xlsx
2025-08-22 15:34:15,633 - INFO - ✅ 成功读取Excel数据，共 30 行
2025-08-22 15:34:16,633 - INFO - 🕒 填写截止时间...
2025-08-22 15:34:16,633 - INFO - 📅 设置截止时间为: 2025-08-29
2025-08-22 15:34:16,698 - INFO - ✅ 找到截止时间输入框，选择器: //div[contains(@class, 'el-date-editor')]//input[@class='el-input__inner']
2025-08-22 15:34:20,781 - INFO - ✅ 成功点击时间输入框
2025-08-22 15:34:26,898 - INFO - ✅ 成功填写截止时间: 2025-08-29
2025-08-22 15:34:26,898 - INFO - ✅ 截止时间填写完成
2025-08-22 15:34:28,898 - INFO - 📝 选择 PPL 类型的评审人方案...
2025-08-22 15:34:28,898 - INFO - 🎯 目标方案: 数据管理员、科长、相关方
2025-08-22 15:34:28,898 - INFO - 🖱️ 步骤1: 点击评审人方案容器...
2025-08-22 15:34:28,898 - INFO - 🎯 点击 PPL 的评审人方案输入框...
2025-08-22 15:34:28,898 - INFO - 🔍 使用输入框ID: el-id-1911-125
2025-08-22 15:34:39,097 - ERROR - ❌ 所有方法都无法点击 PPL 的评审人方案输入框
2025-08-22 15:34:39,098 - ERROR - ❌ 步骤1失败: 无法点击评审人方案容器
2025-08-22 15:34:39,098 - ERROR - ❌ 第二页处理失败: MYHB_PPL_A19-100005