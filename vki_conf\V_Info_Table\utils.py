import pandas as pd
import re
import os

def read_excel_file(file_path):
    """读取 Excel 文件，支持 .xls 和 .xlsx 格式"""
    _, file_ext = os.path.splitext(file_path)
    file_ext = file_ext.lower()

    if file_ext == '.xlsx':
        engine = 'openpyxl'
    elif file_ext == '.xls':
        engine = 'xlrd'
    else:
        raise ValueError("不支持的文件格式，仅支持 .xls 和 .xlsx 文件")

    try:
        excel_data = pd.read_excel(file_path, sheet_name=None, engine=engine, header=None)
        return excel_data
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def process_dataframe(df):
    """处理 DataFrame，设置列名并填充描述列"""
    desc_row_idx = None
    for i, row in df.iterrows():
        if "描述" in row.values:
            desc_row_idx = i
            break

    if desc_row_idx is None or desc_row_idx + 1 >= len(df):
        print("未找到包含‘描述’的行或下方无数据，使用默认列名")
        df.columns = [f"Column_{i}" for i in range(len(df.columns))]
        return df.iloc[desc_row_idx + 1:] if desc_row_idx is not None else df

    desc_col_idx = df.iloc[desc_row_idx].tolist().index("描述")
    header_row = df.iloc[desc_row_idx + 1].copy()
    header_row.iloc[desc_col_idx] = "描述1"
    if desc_col_idx + 1 < len(df.columns):
        header_row.iloc[desc_col_idx + 1] = "描述2"
    if desc_col_idx + 2 < len(df.columns):
        header_row.iloc[desc_col_idx + 2] = "描述3"

    header_row = header_row.fillna('')
    final_columns = []
    nan_counter = 1
    for i, val in enumerate(header_row):
        if val == '' or pd.isna(val):
            final_columns.append(f"Column_{nan_counter}")
            nan_counter += 1
        else:
            final_columns.append(str(val))

    df.columns = final_columns
    df_processed = df.iloc[desc_row_idx + 2:].reset_index(drop=True)

    if "描述1" in df_processed.columns:
        df_processed["描述1"] = df_processed["描述1"].ffill()
    if "描述2" in df_processed.columns:
        df_processed["描述2"] = df_processed["描述2"].ffill()

    df_processed = df_processed.fillna('')
    return df_processed

def has_number(value):
    """检查值是否包含数字"""
    return bool(re.search(r'\d', str(value)))

def count_numbers(value):
    """计算值中的数字个数"""
    return len(re.findall(r'\d+', str(value)))

def is_numeric_only(value):
    """检查值是否只包含数字、小数点和负号"""
    if pd.isna(value) or value == "":
        return True
    return bool(re.match(r'^-?[0-9.]+$', str(value)))