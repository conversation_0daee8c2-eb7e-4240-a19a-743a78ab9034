import sqlite3
from utils import read_excel_file, process_dataframe

def save_to_sqlite(excel_data, db_name='output.db'):
    """将 Excel 数据存入 SQLite 数据库"""

    conn = sqlite3.connect(db_name)
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    for table in tables:
        cursor.execute(f"DROP TABLE {table[0]}")
    conn.commit()

    if not excel_data:
        print("没有数据可保存到数据库")
        conn.close()
        return

    for sheet_name, df in excel_data.items():
        df_processed = process_dataframe(df)
        table_name = sheet_name.replace(" ", "_").replace(".", "_")
        df_processed.to_sql(table_name, conn, if_exists='replace', index=False)
        print(f"表 '{table_name}' 已成功写入数据库 {db_name}")
    conn.close()

def excel_to_sqlite(file_path, db_name='output.db'):
    """主函数：从 Excel 文件到 SQLite 数据库"""
    excel_data = read_excel_file(file_path)
    save_to_sqlite(excel_data, db_name)

if __name__ == "__main__":
    vki_file_path = "data/HTEBU-VKI-CHC-10001_HT出口项目底盘电控关键信息_两厢_002.xlsx"
    excel_to_sqlite(vki_file_path, "data/my_database.db")