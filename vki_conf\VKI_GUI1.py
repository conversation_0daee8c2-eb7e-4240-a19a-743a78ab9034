import sys
import os
import shutil
import subprocess
import re
from datetime import datetime
from pathlib import Path
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QTextEdit, QMessageBox, QGroupBox, QProgressBar, QLineEdit,
    QDesktopWidget
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont


class VKIWorker(QThread):
    """VKI工作线程"""
    progress = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, operation, vki_instance):
        super().__init__()
        self.operation = operation
        self.vki_instance = vki_instance

    def run(self):
        try:
            if self.operation == "v_info_table":
                self.progress.emit("开始处理关键信息表...")
                result = self.vki_instance.run_v_info_table()
                if result:
                    self.finished.emit(True, "关键信息表处理完成")
                else:
                    self.finished.emit(False, "关键信息表处理失败")
            elif self.operation == "v_conf_table":
                self.progress.emit("开始处理配置字表...")
                result = self.vki_instance.run_v_conf_table()
                if result:
                    self.finished.emit(True, "配置字表处理完成")
                else:
                    self.finished.emit(False, "配置字表处理失败")
            elif self.operation == "both":
                self.progress.emit("开始处理配置字表...")
                result1 = self.vki_instance.run_v_conf_table()
                if result1:
                    self.progress.emit("配置字表完成，开始处理关键信息表...")
                    result2 = self.vki_instance.run_v_info_table()
                    if result2:
                        self.finished.emit(True, "两个表格处理完成")
                    else:
                        self.finished.emit(False, "关键信息表处理失败")
                else:
                    self.finished.emit(False, "配置字表处理失败")
        except Exception as e:
            self.finished.emit(False, f"处理失败: {str(e)}")


class VKICore:
    """VKI核心功能类"""
    
    # 常量定义
    SUBPROCESS_TIMEOUT = 300
    ENCODING = 'utf-8'
    
    def __init__(self, gui_instance=None):
        self.base_dir = Path(__file__).parent.absolute()
        
        # 关键信息表独立路径
        self.v_info_input_dir = self.base_dir / "v_info_input"
        self.v_info_output_dir = self.base_dir / "v_info_output"
        self.v_info_work_dir = self.base_dir / "V_Info_Table" / "data"
        self.v_info_work_output_dir = self.base_dir / "V_Info_Table" / "outputs"
        
        # 配置字表独立路径
        self.v_conf_input_dir = self.base_dir / "v_conf_input"
        self.v_conf_output_dir = self.base_dir / "v_conf_output"
        self.v_conf_work_input_dir = self.base_dir / "V_Conf_Table" / "data" / "input"
        self.v_conf_work_output_dir = self.base_dir / "V_Conf_Table" / "data" / "template_output"
        
        self.gui_instance = gui_instance
        self.output_callback = None
        self._ensure_directories()
    
    def set_output_callback(self, callback):
        """设置输出回调函数"""
        self.output_callback = callback
    
    def _print(self, message):
        """输出消息"""
        print(message)
        if self.output_callback:
            self.output_callback(message)
    
    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        directories = [
            self.v_info_input_dir, self.v_info_output_dir, self.v_info_work_dir, self.v_info_work_output_dir,
            self.v_conf_input_dir, self.v_conf_output_dir, self.v_conf_work_input_dir, self.v_conf_work_output_dir
        ]
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _create_timestamped_folder(self, base_path, prefix=""):
        """创建带时间戳的文件夹"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        folder_name = f"{prefix}{timestamp}" if prefix else timestamp
        folder_path = Path(base_path) / folder_name
        folder_path.mkdir(parents=True, exist_ok=True)
        return str(folder_path)
    
    def _move_input_to_history(self, source_dir, target_base_dir):
        """将输入文件移动到历史文件夹"""
        source_path = Path(source_dir)
        if not source_path.exists() or not any(source_path.iterdir()):
            return
        
        history_folder = self._create_timestamped_folder(target_base_dir, "history_")
        for item in source_path.iterdir():
            if item.is_file():
                dst_path = Path(history_folder) / item.name
                shutil.move(str(item), str(dst_path))
                self._print(f"已移动输入文件到历史文件夹: {dst_path}")
    
    def update_config_file(self, config_path, new_filename):
        """更新V_Info_Table/config.py中的INPUT_EXCEL_PATH"""
        try:
            config_file = Path(config_path)
            if not config_file.exists():
                self._print(f"配置文件不存在: {config_path}")
                return
            
            # 安全地读取和写入文件
            backup_path = config_file.with_suffix('.py.backup')
            shutil.copy2(str(config_file), str(backup_path))
            
            content = config_file.read_text(encoding=self.ENCODING)
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                if line.strip().startswith("INPUT_EXCEL_PATH"):
                    lines[i] = f'INPUT_EXCEL_PATH = "data/{new_filename}"'
                    break
            
            config_file.write_text('\n'.join(lines), encoding=self.ENCODING)
            
        except Exception as e:
            self._print(f"更新配置文件失败: {str(e)}")
    
    def run_v_info_table(self):
        """处理关键信息表"""
        try:
            self._print("开始处理关键信息表...")
            
            # 检查输入文件
            if not self.v_info_input_dir.exists():
                self._print("错误: 关键信息表输入文件夹不存在")
                return False
            
            info_files = [f.name for f in self.v_info_input_dir.iterdir() 
                         if f.is_file() and "关键信息" in f.name]
            if not info_files:
                self._print("错误: 关键信息表输入文件夹中没有包含'关键信息'的文件")
                return False
            
            # 创建输出子文件夹
            output_subfolder = self._create_timestamped_folder(self.v_info_output_dir, "v_info_")
            
            for filename in info_files:
                self._print(f"处理文件: {filename}")
                
                # 复制文件到V_Info_Table/data
                src_path = self.v_info_input_dir / filename
                dst_path = self.v_info_work_dir / filename
                shutil.copy2(str(src_path), str(dst_path))
                
                # 更新配置文件
                config_path = self.base_dir / "V_Info_Table" / "config.py"
                if config_path.exists():
                    self.update_config_file(str(config_path), filename)
                
                # 运行V_Info_Table
                self._print(f"运行V_Info_Table处理: {filename}")
                result = subprocess.run([sys.executable, "main.py"], 
                                      cwd=str(self.base_dir / "V_Info_Table"), 
                                      capture_output=True, text=True, 
                                      encoding=self.ENCODING, errors='ignore', 
                                      timeout=self.SUBPROCESS_TIMEOUT)
                
                if result.stdout:
                    self._print(f"V_Info_Table输出: {result.stdout}")
                if result.stderr:
                    self._print(f"V_Info_Table错误: {result.stderr}")
                
                if result.returncode != 0:
                    self._print(f"处理失败，返回码: {result.returncode}")
                    continue
                
                # 复制输出文件到输出子文件夹
                if self.v_info_work_output_dir.exists():
                    for output_file in self.v_info_work_output_dir.iterdir():
                        if output_file.is_file():
                            dst = Path(output_subfolder) / output_file.name
                            shutil.copy2(str(output_file), str(dst))
                            self._print(f"输出文件已保存: {dst}")
            
            # 移动输入文件到历史文件夹
            self._move_input_to_history(str(self.v_info_input_dir), str(self.v_info_input_dir))
            
            self._print("关键信息表处理完成")
            return True
            
        except Exception as e:
            self._print(f"处理关键信息表时出错: {str(e)}")
            return False
    
    def get_user_inputs(self):
        """获取用户输入的参数"""
        if self.gui_instance and hasattr(self.gui_instance, 'model_input') and hasattr(self.gui_instance, 'controller_input'):
            model = self.gui_instance.model_input.text().strip()
            controller = self.gui_instance.controller_input.text().strip()
            
            # 输入验证和清理
            model = self._sanitize_input(model) or "HC2"
            controller = self._sanitize_input(controller) or "D2"
            
            return model, controller
        return "HC2", "D2"
    
    def _sanitize_input(self, input_str):
        """清理用户输入，防止代码注入"""
        if not input_str:
            return ""
        # 只允许字母、数字、下划线和中文字符
        sanitized = re.sub(r'[^\w\u4e00-\u9fff]', '', input_str)
        return sanitized[:50]  # 限制长度
    
    def run_v_conf_table(self):
        """处理配置字表"""
        try:
            self._print("开始处理配置字表...")
            
            # 获取用户输入参数
            model, controller = self.get_user_inputs()
            self._print(f"使用参数: 车型={model}, 控制器={controller}")
            
            # 检查输入文件
            if not self.v_conf_input_dir.exists():
                self._print("错误: 配置字表输入文件夹不存在")
                return False
            
            conf_files = [f.name for f in self.v_conf_input_dir.iterdir() 
                         if f.is_file() and "配置字" in f.name]
            if not conf_files:
                self._print("错误: 配置字表输入文件夹中没有包含'配置字'的文件")
                return False
            
            # 创建输出子文件夹
            output_subfolder = self._create_timestamped_folder(self.v_conf_output_dir, "v_conf_")
            
            # 复制配置字文件到V_Conf_Table输入目录
            for filename in conf_files:
                src_path = self.v_conf_input_dir / filename
                dst_path = self.v_conf_work_input_dir / filename
                shutil.copy2(str(src_path), str(dst_path))
                self._print(f"已复制配置字文件: {filename}")
            
            # 创建临时main.py文件，直接传入参数而不使用input()
            self._create_temp_main_py(model, controller)
            
            # 运行V_Conf_Table
            self._print("运行V_Conf_Table处理...")
            result = subprocess.run([sys.executable, "main_temp.py"], 
                                  cwd=str(self.base_dir / "V_Conf_Table" / "src"), 
                                  capture_output=True, text=True, 
                                  encoding=self.ENCODING, errors='ignore', 
                                  timeout=self.SUBPROCESS_TIMEOUT)
            
            if result.stdout:
                self._print(f"V_Conf_Table输出: {result.stdout}")
            if result.stderr:
                self._print(f"V_Conf_Table错误: {result.stderr}")
            
            if result.returncode != 0:
                self._print(f"处理失败，返回码: {result.returncode}")
                return False
            
            # 移动输出文件到输出子文件夹
            if self.v_conf_work_output_dir.exists():
                for output_file in self.v_conf_work_output_dir.iterdir():
                    if output_file.is_file():
                        dst = Path(output_subfolder) / output_file.name
                        shutil.move(str(output_file), str(dst))
                        self._print(f"输出文件已保存: {dst}")
            
            # 移动输入文件到历史文件夹
            self._move_input_to_history(str(self.v_conf_input_dir), str(self.v_conf_input_dir))
            
            self._print("配置字表处理完成")
            return True
            
        except Exception as e:
            self._print(f"处理配置字表时出错: {str(e)}")
            return False
    
    def _create_temp_main_py(self, model, controller):
        """创建临时main.py文件，直接传入参数"""
        temp_main_content = f"""from excel_processor import ExcelProcessor
from config import ensure_directories
from utils import setup_logging

logger = setup_logging()

def main():
    \"\"\"主程序入口\"\"\"
    try:
        # 确保目录存在
        ensure_directories()

        # 使用GUI传入的参数
        model = "{model}"
        controller = "{controller}"

        if not model or not controller:
            raise ValueError("车型代号和控制器名称不能为空")

        logger.info(f"开始处理：车型={{model}}, 控制器={{controller}}")

        # 创建处理器并运行
        processor = ExcelProcessor(model, controller)
        processor.run()

        logger.info("程序执行完成")
    except Exception as e:
        logger.error(f"程序运行失败：{{str(e)}}")
        raise

if __name__ == "__main__":
    main()
"""
        temp_main_path = self.base_dir / "V_Conf_Table" / "src" / "main_temp.py"
        temp_main_path.write_text(temp_main_content, encoding=self.ENCODING)
    
    def open_folder(self, path):
        """打开文件夹"""
        path_obj = Path(path)
        if not path_obj.exists():
            path_obj.mkdir(parents=True, exist_ok=True)
        if os.name == 'nt':  # Windows
            os.startfile(str(path_obj))
        else:
            QMessageBox.information(None, "提示", f"请手动打开: {path}")
    
    def get_latest_output_folder(self, base_output_dir):
        """获取最新的输出子文件夹"""
        base_path = Path(base_output_dir)
        if not base_path.exists():
            return str(base_path)
        
        subfolders = [f for f in base_path.iterdir() if f.is_dir()]
        if not subfolders:
            return str(base_path)
        
        # 按时间戳排序，返回最新的
        subfolders.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        return str(subfolders[0])


class VKIGUI(QMainWindow):
    # 通用按钮样式
    BUTTON_STYLE_BLUE = """
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 10pt;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """
    
    BUTTON_STYLE_GREEN = """
        QPushButton {
            background-color: #27ae60;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 10pt;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """
    
    BUTTON_STYLE_ORANGE = """
        QPushButton {
            background-color: #f39c12;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px;
            font-size: 11pt;
        }
        QPushButton:hover {
            background-color: #e67e22;
        }
    """
    
    def __init__(self, on_home=None, on_back=None):
        super().__init__()
        self.on_home = on_home
        self.on_back = on_back
        self.core = VKICore(self)
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("关键信息表和配置字表处理工具")
        # 使用相对屏幕大小设置窗口
        desktop = QDesktopWidget()
        screen = desktop.screenGeometry()
        width = min(1200, int(screen.width() * 0.8))
        height = min(800, int(screen.height() * 0.8))
        self.resize(width, height)
        self.move((screen.width() - width) // 2, (screen.height() - height) // 2)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 顶部导航按钮
        nav_container = QWidget()
        nav_layout = QHBoxLayout(nav_container)
        nav_layout.setContentsMargins(0, 0, 0, 10)
        
        # 返回上一级按钮（橙色）
        back_btn = QPushButton("返回上一级")
        back_btn.setFont(QFont("Arial", 11))
        back_btn.setMaximumSize(120, 36)
        if self.on_back:
            back_btn.clicked.connect(self.on_back)
        else:
            back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet(self.BUTTON_STYLE_ORANGE)
        nav_layout.addWidget(back_btn)
        
        # 返回主页按钮（主色蓝色）
        home_btn = QPushButton("返回主页")
        home_btn.setFont(QFont("Arial", 11))
        home_btn.setMaximumSize(120, 36)
        if self.on_home:
            home_btn.clicked.connect(self.on_home)
        else:
            home_btn.clicked.connect(self.close)
        home_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        nav_layout.addWidget(home_btn)
        nav_layout.addStretch()
        main_layout.addWidget(nav_container)
        
        # 创建标题
        title_label = QLabel("关键信息表和配置字表处理工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin: 10px 0;")
        main_layout.addWidget(title_label)
        
        # 创建左右分布的工具区域
        tools_container = QWidget()
        tools_container.setMinimumHeight(350)
        tools_layout = QHBoxLayout(tools_container)
        tools_layout.setSpacing(20)
        
        # 左侧：关键信息表工具（占一半宽度）
        left_group = self.create_v_info_group()
        left_group.setMinimumHeight(330)
        tools_layout.addWidget(left_group, 1)
        
        # 右侧：配置字表工具（占一半宽度）
        right_group = self.create_v_conf_group()
        right_group.setMinimumHeight(330)
        tools_layout.addWidget(right_group, 1)
        
        main_layout.addWidget(tools_container)
        
        # 创建状态显示区域
        self.create_status_area(main_layout)
        
        # 设置整体样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QWidget {
                background-color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin: 10px 0;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
    
    def create_v_info_group(self):
        """创建关键信息表工具组"""
        group = QGroupBox("关键信息表处理工具")
        group.setFont(QFont("Arial", 11, QFont.Bold))
        layout = QVBoxLayout(group)
        
        # 上方适当间距
        layout.addStretch(1)
        
        # 文件夹操作
        folder_layout = QHBoxLayout()
        
        input_btn = QPushButton("打开输入文件夹")
        input_btn.setFont(QFont("Arial", 10))
        input_btn.setFixedSize(150, 38)
        input_btn.clicked.connect(self.open_v_info_input_folder)
        input_btn.setStyleSheet(self.BUTTON_STYLE_BLUE)
        
        output_btn = QPushButton("打开输出文件夹")
        output_btn.setFont(QFont("Arial", 10))
        output_btn.setFixedSize(150, 38)
        output_btn.clicked.connect(self.open_v_info_output_folder)
        output_btn.setStyleSheet(self.BUTTON_STYLE_GREEN)
        
        folder_layout.addWidget(input_btn)
        folder_layout.addWidget(output_btn)
        folder_layout.setSpacing(15)
        layout.addLayout(folder_layout)
        
        # 增加文件夹按钮与运行按钮之间的间距
        layout.addSpacing(25)
        
        # 运行按钮
        run_btn = QPushButton("处理关键信息表")
        run_btn.setFont(QFont("Arial", 12, QFont.Bold))
        run_btn.setFixedSize(180, 50)
        run_btn.clicked.connect(self.process_v_info_table)
        run_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        # 居中放置按钮
        btn_container = QWidget()
        btn_layout = QHBoxLayout(btn_container)
        btn_layout.addStretch()
        btn_layout.addWidget(run_btn)
        btn_layout.addStretch()
        layout.addWidget(btn_container)
        
        # 下方适当间距，使按钮在区域内居中
        layout.addStretch(1)
        
        return group
    
    def create_v_conf_group(self):
        """创建配置字表工具组"""
        group = QGroupBox("配置字表处理工具")
        group.setFont(QFont("Arial", 11, QFont.Bold))
        layout = QVBoxLayout(group)
        
        # 参数设置
        param_layout = QGridLayout()
        param_layout.setSpacing(12)
        param_layout.setContentsMargins(10, 10, 10, 15)
        
        # 车型代号输入
        model_label = QLabel("车型代号:")
        model_label.setFont(QFont("Arial", 10))
        self.model_input = QLineEdit()
        self.model_input.setPlaceholderText("请输入车型代号，如：HC2")
        self.model_input.setText("")
        self.model_input.setFont(QFont("Arial", 10))
        self.model_input.setMinimumHeight(28)
        self.model_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 6px;
                background-color: white;
                font-size: 10pt;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        # 控制器名称输入
        controller_label = QLabel("控制器名称:")
        controller_label.setFont(QFont("Arial", 10))
        controller_label.setToolTip("可输入：D2、D3、域控、onebox（大小写不限）")
        self.controller_input = QLineEdit()
        self.controller_input.setPlaceholderText("可输入：D2、D3、域控、onebox（大小写不限）")
        self.controller_input.setText("")
        self.controller_input.setFont(QFont("Arial", 10))
        self.controller_input.setMinimumHeight(28)
        self.controller_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 6px;
                background-color: white;
                font-size: 10pt;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        param_layout.addWidget(model_label, 0, 0)
        param_layout.addWidget(self.model_input, 0, 1)
        param_layout.addWidget(controller_label, 1, 0)
        param_layout.addWidget(self.controller_input, 1, 1)
        layout.addLayout(param_layout)
        
        # 文件夹操作
        folder_layout = QHBoxLayout()
        
        input_btn = QPushButton("打开输入文件夹")
        input_btn.setFont(QFont("Arial", 10))
        input_btn.setFixedSize(150, 38)
        input_btn.clicked.connect(self.open_v_conf_input_folder)
        input_btn.setStyleSheet(self.BUTTON_STYLE_BLUE)
        
        output_btn = QPushButton("打开输出文件夹")
        output_btn.setFont(QFont("Arial", 10))
        output_btn.setFixedSize(150, 38)
        output_btn.clicked.connect(self.open_v_conf_output_folder)
        output_btn.setStyleSheet(self.BUTTON_STYLE_GREEN)
        
        folder_layout.addWidget(input_btn)
        folder_layout.addWidget(output_btn)
        folder_layout.setSpacing(15)
        layout.addLayout(folder_layout)
        
        # 运行按钮
        run_btn = QPushButton("处理配置字表")
        run_btn.setFont(QFont("Arial", 12, QFont.Bold))
        run_btn.setFixedSize(180, 50)
        run_btn.clicked.connect(self.process_v_conf_table)
        run_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        # 居中放置按钮
        btn_container = QWidget()
        btn_layout = QHBoxLayout(btn_container)
        btn_layout.addStretch()
        btn_layout.addWidget(run_btn)
        btn_layout.addStretch()
        layout.addWidget(btn_container)
        
        return group
    
    def create_status_area(self, main_layout):
        """创建状态显示区域"""
        status_group = QGroupBox("状态信息")
        status_group.setFont(QFont("Arial", 11, QFont.Bold))
        status_layout = QVBoxLayout(status_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        # 状态文本
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(120)
        self.status_text.setReadOnly(True)
        self.status_text.setFont(QFont("Consolas", 9))
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                padding: 8px;
                font-size: 9pt;
                font-family: 'Consolas', monospace;
            }
        """)
        status_layout.addWidget(self.status_text)
        
        main_layout.addWidget(status_group)
    
    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        self.status_text.ensureCursorVisible()
    
    def show_progress(self, show=True):
        """显示/隐藏进度条"""
        self.progress_bar.setVisible(show)
        if show:
            self.progress_bar.setRange(0, 0)  # 无限进度条
        else:
            self.progress_bar.setRange(0, 1)
            self.progress_bar.setValue(1)
    
    def open_v_info_input_folder(self):
        """打开关键信息表输入文件夹"""
        try:
            self.core.open_folder(str(self.core.v_info_input_dir))
            self.log_message(f"打开关键信息表输入文件夹: {self.core.v_info_input_dir}")
        except Exception as e:
            self.log_message(f"打开关键信息表输入文件夹失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")
    
    def open_v_info_output_folder(self):
        """打开关键信息表输出文件夹（最新的子文件夹）"""
        try:
            latest_folder = self.core.get_latest_output_folder(str(self.core.v_info_output_dir))
            self.core.open_folder(latest_folder)
            self.log_message(f"打开关键信息表输出文件夹: {latest_folder}")
        except Exception as e:
            self.log_message(f"打开关键信息表输出文件夹失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")
    
    def open_v_conf_input_folder(self):
        """打开配置字表输入文件夹"""
        try:
            self.core.open_folder(str(self.core.v_conf_input_dir))
            self.log_message(f"打开配置字表输入文件夹: {self.core.v_conf_input_dir}")
        except Exception as e:
            self.log_message(f"打开配置字表输入文件夹失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")
    
    def open_v_conf_output_folder(self):
        """打开配置字表输出文件夹（最新的子文件夹）"""
        try:
            latest_folder = self.core.get_latest_output_folder(str(self.core.v_conf_output_dir))
            self.core.open_folder(latest_folder)
            self.log_message(f"打开配置字表输出文件夹: {latest_folder}")
        except Exception as e:
            self.log_message(f"打开配置字表输出文件夹失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")
    
    def _start_worker(self, operation_type, start_message):
        """启动工作线程的通用方法"""
        self.show_progress(True)
        self.log_message(start_message)
        self.core.set_output_callback(self.log_message)
        self.worker = VKIWorker(operation_type, self.core)
        self.worker.progress.connect(self.log_message)
        self.worker.finished.connect(self.on_operation_finished)
        self.worker.start()
    
    def process_v_info_table(self):
        """处理关键信息表"""
        self._start_worker("v_info_table", "开始处理关键信息表...")
    
    def process_v_conf_table(self):
        """处理配置字表"""
        self._start_worker("v_conf_table", "开始处理配置字表...")
    
    def on_operation_finished(self, success, message):
        """操作完成回调"""
        self.show_progress(False)
        self.log_message(message)
        
        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.critical(self, "错误", message)


def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    window = VKIGUI()
    window.show()
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()