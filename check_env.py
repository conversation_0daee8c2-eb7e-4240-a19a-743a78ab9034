#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
环境检查脚本 - 验证所有依赖包是否正确安装
"""

import sys
import importlib
import subprocess

def check_package(package_name, import_name=None):
    """检查单个包是否可用"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'Unknown')
        print(f"✓ {package_name}: {version}")
        return True
    except ImportError as e:
        print(f"✗ {package_name}: 未安装 ({e})")
        return False

def main():
    """主检查函数"""
    print("=" * 50)
    print("VSETools 环境依赖检查")
    print("=" * 50)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    print("-" * 50)
    
    # 要检查的包列表
    packages = [
        ('PyQt5', 'PyQt5.QtCore'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('scipy', 'scipy'),
        ('matplotlib', 'matplotlib'),
        ('openpyxl', 'openpyxl'),
        ('selenium', 'selenium'),
        ('requests', 'requests'),
        ('Pillow', 'PIL'),
        ('loguru', 'loguru'),
        ('lxml', 'lxml'),
        ('tqdm', 'tqdm'),
        ('cantools', 'cantools'),
        ('python-can', 'can'),
        ('asammdf', 'asammdf'),
        ('pywin32', 'win32api'),
        ('psutil', 'psutil'),
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package_name, import_name in packages:
        if check_package(package_name, import_name):
            success_count += 1
    
    print("-" * 50)
    print(f"检查结果: {success_count}/{total_count} 包可用")
    
    if success_count == total_count:
        print("✓ 所有依赖包都已正确安装！")
        return True
    else:
        print("✗ 部分依赖包缺失，请重新安装")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)