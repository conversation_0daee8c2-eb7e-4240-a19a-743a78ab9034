# VSETools 便携式环境依赖包
# 核心GUI框架
PyQt5==5.15.10

# 数据处理和科学计算
pandas==2.1.4
numpy==1.24.4
scipy==1.11.4
matplotlib==3.8.2

# Excel文件处理
openpyxl==3.1.2
xlrd==2.0.1

# Web自动化
selenium==4.15.2
webdriver-manager==4.0.1

# 文档处理
python-docx==1.1.0

# 图像处理
Pillow==10.1.0

# 网络请求
requests==2.31.0

# 日期时间处理
python-dateutil==2.8.2

# 配置文件处理
pyyaml==6.0.1

# 测试框架
pytest==7.4.3

# CANoe接口
py_canoe==0.1.0

# 日志处理
loguru==0.7.2

# XML处理
lxml==4.9.3

# 进度条
tqdm==4.66.1

# CAN总线和汽车数据处理
cantools==39.4.0
python-can==4.3.1
asammdf==7.4.0

# MATLAB接口（可选）
transplant==0.8.11

# Windows系统集成
pywin32==306
psutil==5.9.6

# 其他工具包
xlsxwriter==3.1.9
chardet==5.2.0