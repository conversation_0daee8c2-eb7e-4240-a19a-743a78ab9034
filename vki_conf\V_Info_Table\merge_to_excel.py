# merge_to_excel.py
import pandas as pd
import sqlite3
import os
import shutil
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, Alignment
from config import DATABASE_PATH, TEMPLATE_PATH


def merge_new_vehicle_table_to_excel(db_path=DATABASE_PATH, template_path=TEMPLATE_PATH, input_file=None):
    """将 new_vehicle_table 合并到基于模板的输出 Excel 文件"""
    # 确保 outputs 目录存在
    output_dir = "outputs"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 动态生成输出文件路径
    if not input_file:
        raise ValueError("必须提供输入文件名以生成输出文件路径")

    input_basename = os.path.splitext(os.path.basename(input_file))[0]
    output_excel_path = os.path.join(output_dir, f"{input_basename}_output.xlsx")

    # 检查模板文件是否存在
    if not os.path.exists(template_path):
        raise FileNotFoundError(f"模板文件未找到: {template_path}")

    # 复制模板到输出路径
    shutil.copy(template_path, output_excel_path)
    print(f"已复制模板到输出文件: {output_excel_path}")

    # 读取 new_vehicle_table
    conn = sqlite3.connect(db_path)
    new_df = pd.read_sql_query("SELECT * FROM new_vehicle_table", conn)
    conn.close()

    # 读取输出 Excel 文件（复制的模板）
    wb = load_workbook(output_excel_path)
    if "整车参数" not in wb.sheetnames:
        raise ValueError("模板文件中未找到 '整车参数' sheet")
    ws = wb["整车参数"]

    # 清除现有合并单元格
    for merged_range in list(ws.merged_cells.ranges):
        ws.unmerge_cells(str(merged_range))

    # 获取现有数据（表头占一行，从第2行开始为数据）
    existing_data = pd.DataFrame(ws.values)
    existing_headers = existing_data.iloc[0].fillna('')  # 表头一行
    existing_content = existing_data.iloc[1:].reset_index(drop=True)  # 数据部分

    # 准备 new_vehicle_table 的表头和数据
    new_headers = ["信号名", "中文名", "单位"] + new_df.columns[3:].tolist()  # 保留车型列
    new_content = new_df.rename(columns={"信号定义": "信号名", "信号名称": "中文名", "单位": "单位"})

    # 添加分类列
    category_col = pd.DataFrame({
        "分类": ["整车参数"] + [""] * (len(new_content) - 1)
    })

    # 合并数据
    merged_headers = pd.concat([pd.Series(["分类"]), pd.Series(new_headers), existing_headers], axis=0,
                               ignore_index=True)
    merged_content = pd.concat([category_col, new_content, existing_content], axis=1)

    # 写入 Excel（逐单元格写入）
    ws.delete_rows(1, ws.max_row)  # 清空现有内容
    for c_idx, value in enumerate(merged_headers):
        cell = ws.cell(row=1, column=c_idx + 1)
        cell.value = value
        cell.font = Font(name="宋体", size=11)
        cell.alignment = Alignment(horizontal="center", vertical="center")

    for r_idx, row in merged_content.iterrows():
        for c_idx, value in enumerate(row):
            cell = ws.cell(row=r_idx + 2, column=c_idx + 1)
            cell.value = value
            cell.font = Font(name="宋体", size=11)
            cell.alignment = Alignment(horizontal="center", vertical="center")

    # 合并分类列（除表头外）
    ws.merge_cells(start_row=2, start_column=1, end_row=1 + len(new_content), end_column=1)
    ws.cell(row=2, column=1).alignment = Alignment(horizontal="center", vertical="center")

    # 获取车型列的表头名称
    vehicle_columns = new_df.columns[3:].tolist()
    print("车型列名称:", vehicle_columns)

    # 调整列宽
    for col in range(1, ws.max_column + 1):
        max_length = 0
        column = get_column_letter(col)
        header_value = ws.cell(row=1, column=col).value
        for cell in ws[column]:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = 10 if header_value in vehicle_columns else min((max_length + 2) * 1.2, 30)
        print(
            f"列 {column} ({'车型列' if header_value in vehicle_columns else '非车型列'}: {header_value}) 设置宽度为 {adjusted_width}")
        ws.column_dimensions[column].width = adjusted_width

    # 保存文件
    wb.save(output_excel_path)
    print(f"已成功将 new_vehicle_table 合并到 {output_excel_path} 的 '整车参数' sheet 中")

    return output_excel_path


if __name__ == "__main__":
    # 示例调用
    input_file = "data/HTEBU-VKI-CHC-10001_HT出口项目底盘电控关键信息_两厢_002.xlsx"
    merge_new_vehicle_table_to_excel(input_file=input_file)