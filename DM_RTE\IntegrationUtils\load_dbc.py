from time import perf_counter

import canmatrix
import canmatrix.copy
import  canmatrix.formats
import yaml

def loadVSESignalsFromDBC(dbc_path,vse_signal_yaml):
    db = canmatrix.formats.loadp_flat(dbc_path)
    frame_dict_id = dict()
    for frame in db.frames:
        frame_dict_id[frame.arbitration_id.id] = frame
    vse_input_signal_dict = dict()
    with open(vse_signal_yaml, 'r') as file:
        data = yaml.load(file, Loader=yaml.FullLoader)
        for i in data:
            for j in data[i]:
                try:
                    found_frame = frame_dict_id[i]
                except Exception:
                    continue
                if found_frame is not None:
                    for sig in found_frame.signals:
                        if sig.start_bit == data[i][j]["Start_Bit"] and sig.size == data[i][j]["Length"]:
                            vse_input_signal_dict[j] = sig.name
    return vse_input_signal_dict

if __name__ == '__main__':
    print(loadVSESignalsFromDBC(r"E:\项目\新建文件夹\0823\SZ西安出差\VSE_SZ_CANFD20240618.dbc",'vse_dbc_signal.yaml'))
