@echo off
chcp 65001 >nul
echo 正在安装VSETools依赖包...

:: 设置项目根目录
set PROJECT_DIR=%~dp0
cd /d "%PROJECT_DIR%"

:: 检查WinPython
set WINPYTHON_DIR=%PROJECT_DIR%Winpython31210
set WINPYTHON_EXE=%WINPYTHON_DIR%\python\python.exe
set PIP_EXE=%WINPYTHON_DIR%\python\Scripts\pip.exe

if not exist "%WINPYTHON_EXE%" (
    echo 错误：未找到 Winpython31210 文件夹！
    echo 请确保将 Winpython31210 文件夹放在项目根目录下。
    pause
    exit /b 1
)

echo 使用WinPython环境安装依赖包...
echo Python路径: %WINPYTHON_EXE%

:: 检查requirements.txt
if not exist "requirements.txt" (
    echo 创建基础requirements.txt文件...
    echo PyQt5==5.15.9> requirements.txt
    echo pandas>> requirements.txt
    echo numpy>> requirements.txt
    echo openpyxl>> requirements.txt
)

:: 安装依赖包
echo 正在安装依赖包，请稍候...
"%PIP_EXE%" install -r requirements.txt

if errorlevel 1 (
    echo.
    echo 安装失败！请检查网络连接或手动安装。
    pause
    exit /b 1
) else (
    echo.
    echo 依赖包安装完成！
    echo 现在可以运行 VSETools.bat 启动程序了。
    pause
)