@echo off
chcp 65001 >nul
echo ========================================
echo VSETools 便携版打包脚本
echo ========================================

:: 设置日期格式 (使用更可靠的方法)
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~0,4%"
set "MM=%dt:~4,2%"
set "DD=%dt:~6,2%"
set PACKAGE_NAME=VSETools_Portable_%YY%%MM%%DD%
set SOURCE_DIR=%~dp0
set PACKAGE_DIR=%SOURCE_DIR%%PACKAGE_NAME%

echo 创建打包目录: %PACKAGE_NAME%

:: 创建打包目录
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"

:: 复制项目文件 (排除package_for_distribution.bat自身)
echo 复制项目文件...
xcopy "%SOURCE_DIR%*.py" "%PACKAGE_DIR%\" /I /H /Y
echo 复制.bat文件（排除打包脚本自身）...
for %%f in ("%SOURCE_DIR%*.bat") do (
    if /i not "%%~nxf"=="package_for_distribution.bat" (
        copy "%%f" "%PACKAGE_DIR%\"
    )
)
xcopy "%SOURCE_DIR%*.txt" "%PACKAGE_DIR%\" /I /H /Y
xcopy "%SOURCE_DIR%*.md" "%PACKAGE_DIR%\" /I /H /Y
echo 复制.vbs文件...
xcopy "%SOURCE_DIR%*.vbs" "%PACKAGE_DIR%\" /I /H /Y
:: 注意：不复制.lnk文件，因为它们包含绝对路径，在目标机器上可能无法正常工作

:: 复制项目模块
echo 复制项目模块...
if exist "%SOURCE_DIR%Apply_Fillin_Upload" xcopy "%SOURCE_DIR%Apply_Fillin_Upload" "%PACKAGE_DIR%\Apply_Fillin_Upload\" /E /I /H /Y
if exist "%SOURCE_DIR%AutoCali" xcopy "%SOURCE_DIR%AutoCali" "%PACKAGE_DIR%\AutoCali\" /E /I /H /Y
if exist "%SOURCE_DIR%DM_RTE" xcopy "%SOURCE_DIR%DM_RTE" "%PACKAGE_DIR%\DM_RTE\" /E /I /H /Y
if exist "%SOURCE_DIR%vki_conf" xcopy "%SOURCE_DIR%vki_conf" "%PACKAGE_DIR%\vki_conf\" /E /I /H /Y

:: 复制虚拟环境
echo 复制Python虚拟环境...
if exist "%SOURCE_DIR%venv_vsetools" xcopy "%SOURCE_DIR%venv_vsetools" "%PACKAGE_DIR%\venv_vsetools\" /E /I /H /Y

:: 创建使用说明
echo 创建使用说明...
echo VSETools 1.0 便携版 > "%PACKAGE_DIR%\使用说明.txt"
echo. >> "%PACKAGE_DIR%\使用说明.txt"
echo 1. 双击"启动VSETools_便携版.bat"运行程序 >> "%PACKAGE_DIR%\使用说明.txt"
echo 2. 如遇问题，运行"check_env.py"检查环境 >> "%PACKAGE_DIR%\使用说明.txt"
echo 3. 双击"create_shortcut.bat"在桌面创建VSETools1.0快捷方式 >> "%PACKAGE_DIR%\使用说明.txt"
echo 4. 本版本包含完整Python环境，无需额外安装 >> "%PACKAGE_DIR%\使用说明.txt"
echo. >> "%PACKAGE_DIR%\使用说明.txt"
echo 系统要求: >> "%PACKAGE_DIR%\使用说明.txt"
echo - Windows 10/11 >> "%PACKAGE_DIR%\使用说明.txt"
echo - 2GB RAM以上 >> "%PACKAGE_DIR%\使用说明.txt"
echo - 1GB可用磁盘空间 >> "%PACKAGE_DIR%\使用说明.txt"

echo ========================================
echo 打包完成！
echo 打包目录: %PACKAGE_DIR%
echo ========================================
echo 现在可以将整个文件夹复制到其他电脑使用
pause