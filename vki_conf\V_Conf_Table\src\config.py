import os
from pathlib import Path

# 项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent

# 输入和输出目录
INPUT_DIR = BASE_DIR / "data" / "input"
OUTPUT_DIR = BASE_DIR / "data" / "output"
TEMPLATE_DIR = BASE_DIR / "data" / "template"
TEMPLATE_OUTPUT_DIR = BASE_DIR / "data" / "template_output"

# 日志文件路径
LOG_FILE = BASE_DIR / "logs" / "process.log"

# 控制器与Sheet名称的映射
CONTROLLER_SHEET_MAPPING = {
    "d2": r"^[A-Z0-9]+(-[A-Z0-9]+)*$",
    "domain": ["左域", "前域", "后域"],
    "域控": lambda name: len(name) == 2 and name[1] == "域",  # 新增：匹配两个字且第二个字为“域”
}

# 输出列的字符长度规则
COLUMN_LENGTH_RULES = {
    "B": 16,
    "C": 2,
    "D": 2,
    "E": 2,
    "F": 2,
    "G": 2,
    "H": 2,
    "I": 2,
    "J": 2,
}

# 默认输出文件名模板
OUTPUT_FILENAME_TEMPLATE = "{model}_ConfigWord_{controller}.xlsx"

def ensure_directories():
    """确保输入、输出、日志、模板和模板输出目录存在"""
    INPUT_DIR.mkdir(parents=True, exist_ok=True)
    OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    TEMPLATE_DIR.mkdir(parents=True, exist_ok=True)
    TEMPLATE_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    LOG_FILE.parent.mkdir(parents=True, exist_ok=True)