#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加vki_conf路径到sys.path
vki_path = os.path.join(os.path.dirname(__file__), 'vki_conf')
sys.path.append(vki_path)

from PyQt5.QtWidgets import QApplication
from vki_conf.VKI_GUI import VK<PERSON><PERSON>

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 创建VKI GUI窗口
    window = VKIGUI()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()