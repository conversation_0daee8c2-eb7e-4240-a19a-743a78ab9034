@echo off
chcp 65001 >nul
echo 正在为Python 3.8环境安装AutoCali依赖包...

:: 设置项目根目录
set PROJECT_DIR=%~dp0
cd /d "%PROJECT_DIR%"

:: 检查WinPython 3.8
set WINPYTHON38_DIR=%PROJECT_DIR%Winpython30810
set WINPYTHON38_EXE=%WINPYTHON38_DIR%\python-3.8.10.amd64\python.exe
set PIP38_EXE=%WINPYTHON38_DIR%\python-3.8.10.amd64\Scripts\pip.exe

if not exist "%WINPYTHON38_EXE%" (
    echo 错误：未找到 Winpython30810 文件夹！
    echo 请确保将 Winpython30810 文件夹放在项目根目录下。
    pause
    exit /b 1
)

echo 使用WinPython 3.8环境安装依赖包...
echo Python 3.8路径: %WINPYTHON38_EXE%

:: 检查requirements_python38.txt
if not exist "requirements_python38.txt" (
    echo 错误：未找到requirements_python38.txt文件！
    pause
    exit /b 1
)

:: 安装依赖包
echo 正在安装Python 3.8依赖包，请稍候...
"%PIP38_EXE%" install -r requirements_python38.txt

if errorlevel 1 (
    echo.
    echo 基础包安装失败！请检查网络连接。
    pause
    exit /b 1
) else (
    echo.
    echo Python 3.8基础依赖包安装完成！
    echo.
    echo 重要提示：
    echo 1. 如需使用MATLAB Engine，请确保已安装MATLAB
    echo 2. 然后在MATLAB安装目录下运行以下命令安装matlab.engine：
    echo    cd "matlabroot\extern\engines\python"
    echo    "%WINPYTHON38_EXE%" setup.py install
    echo.
    echo 或者尝试使用pip安装：
    echo    "%PIP38_EXE%" install matlabengine
    echo.
    pause
)