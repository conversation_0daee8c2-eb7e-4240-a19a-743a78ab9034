import os.path
import struct
from itertools import cycle
from signal import signal

import openpyxl
import canmatrix
from RTETestUtils.slddUtils import sldd_signal_set
from tqdm import tqdm
import yaml

import time




def loadFromXlsx(file):
    all_ecu_names = []
    workbook = openpyxl.open(file)
    sheet = workbook._sheets[0]
    db = canmatrix.CanMatrix()

    db.add_frame_defines("GenMsgDelayTime", 'INT 0 65535')
    db.add_frame_defines("GenMsgCycleTimeActive", 'INT 0 65535')
    db.add_frame_defines("GenMsgNrOfRepetitions", 'INT 0 65535')

    db.add_signal_defines("GenSigSNA", 'STRING')

    ecu_start = ecu_end = 0
    i = 1
    column_heads = []
    while isinstance(sheet.cell(1, i).value, str):
        column_heads.append(sheet.cell(1, i).value)
        i = i + 1
    frame_id = None
    signal_name = ""
    signal_length = 8
    new_frame = None  # type: typing.Optional[canmatrix.Frame]
    new_signal = None  # type: typing.Optional[canmatrix.Signal]

    def get_if_possible(my_row, my_value, default=None):
        if my_value in column_heads and my_row[column_heads.index(my_value)].value is not None:
            return my_row[column_heads.index(my_value)].value
        else:
            return default

    bar = tqdm(total=sheet.max_row)
    signals_set = set()
    for row in sheet.rows:
        bar.update(1)
        if row[column_heads.index('Msg ID\n报文ID')].value is not None and "0x" not in row[
            column_heads.index('Msg ID\n报文ID')].value:
            continue

        if row[column_heads.index('Msg ID\n报文ID')].value != frame_id and row[
            column_heads.index('Msg ID\n报文ID')].value is not None:
            frame_id = row[column_heads.index('Msg ID\n报文ID')].value
            frame_name = row[column_heads.index('Msg Name\n报文名称')].value
            cycle_time = get_if_possible(row, 'Msg Cycle Time (ms)\n报文周期时间', 0)
            launch_type = get_if_possible(row, 'Launch Type')
            dlc = get_if_possible(row, 'DLC\n报文长度')
            new_frame = canmatrix.Frame(frame_name, arbitration_id=int(frame_id, 16), size=dlc)

            for col_head in column_heads:
                if col_head.startswith("frame."):
                    command_str = col_head.replace("frame", "new_frame")
                    command_str += "=" + str(row[column_heads.index(col_head)].value)
                    exec(command_str)

            db.add_frame(new_frame)
            if launch_type is not None:
                new_frame.add_attribute("GenMsgSendType", launch_type)
                # if launch_type not in launch_types:
                #     launch_types.append(launch_type)

            try:
                if cycle_time is not None:
                    new_frame.cycle_time = int(cycle_time)
            except Exception:
                new_frame.cycle_time = 10
        else:
            if get_if_possible(row, 'Signal Name\n信号名称(英文)') != signal_name and get_if_possible(row,
                                                                                                      'Signal Name\n信号名称(英文)') is not None and get_if_possible(
                row, 'Signal Name\n信号名称(英文)') not in signals_set:
                signal_name = get_if_possible(row, 'Signal Name\n信号名称(英文)')
                start_byte = int(get_if_possible(row, 'Start Byte\n起始字节', "0"))
                start_bit = int(get_if_possible(row, 'Start Bit\n起始位', "0"))
                signal_comment = get_if_possible(row, 'Signal Name\n信号名称(中文)')
                signal_length = int(get_if_possible(row, 'Bit Length (Bit)\n信号长度', 0))
                signal_datetype = get_if_possible(row, 'Date Type\n数据类型')
                signal_byte_order = get_if_possible(row, 'Byte Order\n排列格式')
                if signal_byte_order is not None:
                    if 'In' in signal_byte_order:
                        is_little_endian = True
                    else:
                        is_little_endian = False
                else:
                    is_little_endian = True
                if signal_datetype == "Unsigned":
                    is_signed = False
                else:
                    is_signed = True

                multiplex = None

                signal_child_ID = get_if_possible(row, 'Sub ID\n子ID')
                if signal_child_ID is not None:
                    if 'Multiplexor' == signal_child_ID:
                        multiplex = 'Multiplexor'


                    else:
                        if '：' in signal_child_ID:
                            multiplex = int(str.split(signal_child_ID, '：')[1], 16)
                        else:
                            multiplex = int(str.split(signal_child_ID, ':')[1], 16)

                if signal_name != "":
                    new_signal = canmatrix.Signal(signal_name, start_bit=start_bit, size=signal_length,
                                                  is_little_endian=is_little_endian, is_signed=is_signed,
                                                  multiplex=multiplex)
                    if not is_little_endian:
                        # motorola
                        if "lsb" in signal_byte_order or "LSB" in signal_byte_order:
                            motorola_bit_format = "lsb"
                        elif "MSB" in signal_byte_order or "msb" in signal_byte_order:
                            motorola_bit_format = "msb"
                        else:
                            motorola_bit_format = "msbreverse"

                        if motorola_bit_format == "msb":
                            new_signal.set_startbit(start_bit, bitNumbering=1)
                        elif motorola_bit_format == "msbreverse":
                            new_signal.set_startbit(start_bit)
                        else:  # motorola_bit_format == "lsb"
                            new_signal.set_startbit(
                                start_bit,
                                bitNumbering=1,
                                startLittle=True
                            )

                    new_frame.add_signal(new_signal)
                    new_signal.add_comment(signal_comment)
                    value = get_if_possible(row, 'Default Value (Hex)\n默认值')
                    if value is not None:
                        value = str(value)
                    value_name = get_if_possible(row, 'Signal Value Description\n枚举值/公式(英文)')
                    factor = get_if_possible(row, 'Resolution\n精度')
                    if factor is not None:
                        tmp_str = str(factor)
                        if "=" in tmp_str:
                            tmp_str = str.split(tmp_str, "=")[1]
                        new_signal.factor = float(eval(tmp_str))

                    offset = get_if_possible(row, "Offset\n偏移量")
                    if offset is not None:
                        tmp_str = str(offset)
                        if '=' in tmp_str:
                            tmp_str = str.split(tmp_str, '=')[1]
                        new_signal.offset = new_signal.float_factory(eval(tmp_str))

                    mini = get_if_possible(row, 'Signal Min. Value (phys)\n物理最小值')
                    if mini is not None:
                        tmp_str = str(mini)
                        if '=' in tmp_str:
                            tmp_str = str.split(tmp_str, '=')[1]
                        new_signal.min = new_signal.float_factory(eval(tmp_str))

                    maxi = get_if_possible(row, 'Signal Max. Value (phys)\n物理最大值')
                    if maxi is not None:
                        try:
                            tmp_str = str(maxi)
                            if '=' in tmp_str:
                                tmp_str = str.split(tmp_str, '=')[1]
                            new_signal.max = new_signal.float_factory(eval(tmp_str))
                        except Exception:
                            new_signal.max = new_signal.float_factory(65535)
                signals_set.add(signal_name)

            for col_head in column_heads:
                if col_head.startswith("signal."):
                    command_str = col_head.replace("signal", "new_signal")
                    command_str += "=" + str(row[column_heads.index(col_head)].value)
                    exec(command_str)

    bar.close()
    for frame in db.frames:
        frame.update_receiver()
        frame.calc_dlc()

    db.set_fd_type()
    return db


def gen_vse_signals_dict(can_matrix,vse_input_signals_config,save_dbc_path):
    db = loadFromXlsx(can_matrix)
    file_name, extension = os.path.splitext(os.path.basename(can_matrix))
    canmatrix.formats.dumpp({"": db}, os.path.join(save_dbc_path,file_name+".dbc"))
    vse_input_signal_dict = dict()
    with open(vse_input_signals_config, 'r') as file:
        data = yaml.load(file, Loader=yaml.FullLoader)
        for i in data:
            for j in data[i]:
                try:
                    found_frame = db.get_frame_by_id(i)
                except Exception:
                    continue
                if found_frame is not None:
                    for sig in found_frame.signals:
                        if sig.start_bit == data[i][j]["Start_Bit"] and sig.size == data[i][j]["Length"]:
                            vse_input_signal_dict[j] = sig.name
    return vse_input_signal_dict

if __name__ == '__main__':
   print(gen_vse_signals_dict("SGHL can矩阵——最新.xlsx","vse_dbc_signal.yaml","E:\pythonProject7"))
  # db = loadFromXlsx(r"E:\下载\HB_域控_CAN矩阵1.0.0.xlsx")
   #canmatrix.formats.dumpp({"":db},"HB.dbc")