import re
import logging
import shutil
from pathlib import Path
from typing import List, Dict
import openpyxl
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.styles import Alignment, Font
from openpyxl.utils import get_column_letter

from config import INPUT_DIR, OUTPUT_DIR, TEMPLATE_DIR, TEMPLATE_OUTPUT_DIR, CONTROLLER_SHEET_MAPPING, OUTPUT_FILENAME_TEMPLATE
from utils import setup_logging, is_valid_sheet_name, is_strikethrough, get_merged_cell_value, apply_yellow_fill, copy_cell_format

logger = setup_logging()

class WarningCollector(logging.Handler):
    """收集WARNING级别的日志"""
    def __init__(self):
        super().__init__()
        self.warnings = []

    def emit(self, record):
        if record.levelno == logging.WARNING:
            self.warnings.append(record.getMessage())

    def print_warnings(self):
        """打印所有收集的WARNING日志"""
        if self.warnings:
            logger.info("=== 汇总所有WARNING日志 ===")
            for warning in self.warnings:
                logger.info(warning)
        else:
            logger.info("无WARNING日志")

class ExcelProcessor:
    """处理配置字Excel文件的类"""

    def __init__(self, model: str, controller: str):
        """初始化处理器"""
        self.model = model.upper()
        self.controller = controller
        self.controller_lower = controller.lower()  # 保持原样，支持中文控制器
        self.workbook = None
        self.output_workbook = openpyxl.Workbook()
        self.output_sheet = self.output_workbook.active
        self.output_sheet.title = "ConfigWord"
        self.current_row = 1
        self.warning_collector = WarningCollector()
        logger.addHandler(self.warning_collector)
        self.max_extra_cols = 0

    def find_config_files(self) -> List[Path]:
        """查找包含控制器名称和“配置字”的Excel文件"""
        try:
            files = [
                f for f in INPUT_DIR.glob("*.xlsx")
                if "配置字" in f.name and self.controller_lower in f.name.lower()
            ]
            if not files:
                logger.warning(f"未找到包含'{self.controller}'和'配置字'的Excel文件")
            else:
                logger.info(f"找到{len(files)}个配置文件：{[f.name for f in files]}")
            return files
        except Exception as e:
            logger.error(f"查找配置文件时出错：{str(e)}")
            raise

    def prepare_template_file(self) -> Path:
        """复制模板文件到template_output目录并重命名"""
        try:
            template_files = list(TEMPLATE_DIR.glob("*.xlsx"))
            if not template_files:
                logger.warning(f"未找到{str(TEMPLATE_DIR)}中的模板文件")
                return None
            if len(template_files) > 1:
                logger.warning(f"在{str(TEMPLATE_DIR)}中找到多个模板文件：{[f.name for f in template_files]}，使用第一个：{template_files[0].name}")
            template_file = template_files[0]
            logger.info(f"找到模板文件：{template_file.name}")

            src_filename = template_file.name
            new_filename = re.sub(r"[A-Za-z]{2}(?=车型)", self.model, src_filename)
            dest_path = TEMPLATE_OUTPUT_DIR / new_filename

            shutil.copy(template_file, dest_path)
            logger.info(f"模板文件已复制并重命名为：{dest_path}")

            return dest_path
        except Exception as e:
            logger.error(f"复制或重命名模板文件时出错：{str(e)}")
            raise

    def process_sheets(self, file_path: Path) -> List[Dict]:
        """处理Excel文件中的符合条件的Sheet"""
        results = []
        try:
            self.workbook = openpyxl.load_workbook(file_path, data_only=True)
            sheet_rule = CONTROLLER_SHEET_MAPPING.get(self.controller_lower, r"^[A-Z0-9]+(-[A-Z0-9]+)*$")
            logger.info(f"处理文件：{file_path.name}，Sheet规则：{sheet_rule}")

            for sheet_name in self.workbook.sheetnames:
                if is_valid_sheet_name(sheet_name, sheet_rule):
                    logger.info(f"处理Sheet：{sheet_name}")
                    sheet_results = self.process_single_sheet(self.workbook[sheet_name])
                    results.extend(sheet_results)
                else:
                    logger.debug(f"跳过Sheet：{sheet_name}（不符合规则）")

            self.workbook.close()
            return results
        except Exception as e:
            logger.error(f"处理文件{file_path.name}时出错：{str(e)}")
            raise

    def process_single_sheet(self, ws: Worksheet) -> List[Dict]:
        """处理单个Sheet，提取配置字信息及右侧所有内容"""
        from config import COLUMN_LENGTH_RULES
        results = []
        try:
            input_filename = "配置字总表"
            sheet_name = ws.title

            # 计算实际使用的行数和列数
            max_row = 1
            max_col = 1
            for row in ws.iter_rows():
                for cell in row:
                    if cell.value is not None:
                        max_row = max(max_row, cell.row)
                        max_col = max(max_col, cell.column)
            logger.info(f"[{input_filename}/{sheet_name}] 实际行数={max_row}, 列数={max_col}, 合并单元格数={len(ws.merged_cells.ranges)}")

            byte_row, byte_col = None, None
            for row in range(1, max_row + 1):
                for col in range(1, max_col - 7):
                    if all(ws.cell(row=row, column=col+i).value == f"Byte{i+1}" for i in range(8)):
                        byte_row, byte_col = row, col
                        break
                if byte_row:
                    break

            if not byte_row:
                logger.warning(f"[{input_filename}/{sheet_name}] 未找到Byte1到Byte8的连续单元格，跳过")
                return results

            variant_cell = ws.cell(row=byte_row+1, column=byte_col-1)
            model_cell = ws.cell(row=byte_row+1, column=byte_col-2)
            if model_cell.value != "车型":
                logger.warning(f"[{input_filename}/{sheet_name}] 的Byte1下方未找到‘车型’单元格，跳过")
                return results

            is_model_merged = False
            for merged_range in ws.merged_cells.ranges:
                if merged_range.min_row == byte_row and merged_range.max_row == byte_row:
                    if merged_range.min_col <= 3 and merged_range.max_col >= 4:  # C=3, D=4
                        is_model_merged = True
                        break
            if not is_model_merged:
                logger.warning(f"[{input_filename}/{sheet_name}] 的车型单元格上方未合并，跳过")
                return results

            # 缓存合并单元格范围
            merged_ranges = [
                {
                    "range": merged_range,
                    "min_row": merged_range.min_row,
                    "max_row": merged_range.max_row,
                    "min_col": merged_range.min_col,
                    "max_col": merged_range.max_col
                }
                for merged_range in ws.merged_cells.ranges
            ]

            # 提取Byte1到Byte8同行右侧的内容（row=byte_row）
            extra_header_row1 = []
            merged_ranges_row1 = []
            for col in range(byte_col+8, max_col + 1):
                cell = ws.cell(row=byte_row, column=col)
                value = get_merged_cell_value(ws, byte_row, col)
                extra_header_row1.append({"value": value, "cell": cell})
                for merged in merged_ranges:
                    if (merged["min_row"] <= byte_row <= merged["max_row"] and
                        merged["min_col"] <= col <= merged["max_col"]):
                        if merged["min_col"] >= byte_col+8 and merged["min_row"] == byte_row:
                            merged_ranges_row1.append({
                                "start_col": merged["min_col"] - (byte_col+8) + 11,
                                "end_col": merged["max_col"] - (byte_col+8) + 11,
                                "value": value
                            })
                            break

            # 提取Byte1到Byte8下方一行右侧的内容（row=byte_row+1）
            extra_header_row2 = []
            for col in range(byte_col+8, max_col + 1):
                cell = ws.cell(row=byte_row+1, column=col)
                value = get_merged_cell_value(ws, byte_row+1, col)
                extra_header_row2.append({"value": value, "cell": cell})

            self.max_extra_cols = max(self.max_extra_cols, len(extra_header_row1), len(extra_header_row2))

            header_data = {
                "model_upper": get_merged_cell_value(ws, byte_row, byte_col-2),
                "byte1": ws.cell(row=byte_row, column=byte_col).value,
                "byte2": ws.cell(row=byte_row, column=byte_col+1).value,
                "byte3": ws.cell(row=byte_row, column=byte_col+2).value,
                "byte4": ws.cell(row=byte_row, column=byte_col+3).value,
                "byte5": ws.cell(row=byte_row, column=byte_col+4).value,
                "byte6": ws.cell(row=byte_row, column=byte_col+5).value,
                "byte7": ws.cell(row=byte_row, column=byte_col+6).value,
                "byte8": ws.cell(row=byte_row, column=byte_col+7).value,
                "model_lower": model_cell.value,
                "variant": variant_cell.value,
                "merged1": get_merged_cell_value(ws, byte_row+1, byte_col),
                "single1": ws.cell(row=byte_row+1, column=byte_col+2).value,
                "merged2": get_merged_cell_value(ws, byte_row+1, byte_col+3),
                "single2": ws.cell(row=byte_row, column=byte_col+5).value,
                "single3": ws.cell(row=byte_row, column=byte_col+6).value,
                "single4": ws.cell(row=byte_row, column=byte_col+7).value,
                "extra_header_row1": extra_header_row1,
                "extra_header_row2": extra_header_row2,
                "merged_ranges_row1": merged_ranges_row1,
            }

            for row in range(byte_row+2, max_row + 1):
                cell = ws.cell(row=row, column=byte_col-2)
                if not cell.value or is_strikethrough(cell):
                    continue
                if isinstance(cell.value, str) and cell.value.startswith(self.model):
                    row_data = {}
                    issues = []
                    for idx, output_col in enumerate(["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"]):
                        col = byte_col - 2 + idx
                        cell = ws.cell(row=row, column=col)
                        value = cell.value

                        if output_col != "A":
                            clean_value = str(value).strip() if value is not None else ""
                            expected_length = COLUMN_LENGTH_RULES.get(output_col)
                            if value is None or clean_value == "":
                                issues.append(f"[输出表] 列{output_col} 为空")
                            elif len(clean_value) != expected_length:
                                issues.append(
                                    f"[输出表] 列{output_col} 值='{clean_value}' 长度{len(clean_value)} "
                                    f"不符合预期{expected_length}"
                                )

                        row_data[output_col] = {"value": value, "cell": cell}

                    extra_row_data = []
                    for col in range(byte_col+8, max_col + 1):
                        value = get_merged_cell_value(ws, row, col)
                        if value is not None and str(value).strip():
                            extra_row_data.append({"value": value, "cell": ws.cell(row=row, column=col)})

                    row_data["extra"] = extra_row_data

                    results.append({
                        "header": header_data,
                        "row": row_data,
                        "issues": issues,
                        "input_row": row,
                        "input_file": input_filename,
                        "input_sheet": sheet_name
                    })
                    if issues:
                        logger.warning(
                            f"[{input_filename}/{sheet_name}] 行{row} -> {'; '.join(issues)}"
                        )

            logger.info(f"[{input_filename}/{sheet_name}] 找到{len(results)}条符合条件的记录")
            return results
        except Exception as e:
            logger.error(f"[{input_filename}/{sheet_name}] 处理时出错：{str(e)}")
            raise

    def write_output(self, results: List[Dict]):
        """将结果写入新的Excel文件，包括右侧所有内容"""
        try:
            self.output_sheet.column_dimensions["A"].width = 25
            self.output_sheet.column_dimensions["B"].width = 25
            for col in range(3, 11):
                self.output_sheet.column_dimensions[get_column_letter(col)].width = 10
            for col in range(11, 11 + self.max_extra_cols):
                self.output_sheet.column_dimensions[get_column_letter(col)].width = 20

            self.output_sheet.row_dimensions[1].height = 30
            self.output_sheet.row_dimensions[2].height = 30

            header_font = Font(name="Microsoft YaHei", size=14)
            center_alignment = Alignment(horizontal="center", vertical="center")

            grouped_results = {}
            for result in results:
                key = (result["input_file"], result["input_sheet"])
                if key not in grouped_results:
                    grouped_results[key] = []
                grouped_results[key].append(result)

            if results:
                first_header = results[0]["header"]
                self.output_sheet.merge_cells("A1:B1")
                cell = self.output_sheet["A1"]
                cell.value = first_header["model_upper"]
                cell.font = header_font
                cell.alignment = center_alignment

                for col, key in enumerate(["byte1", "byte2", "byte3", "byte4", "byte5", "byte6", "byte7", "byte8"], 3):
                    cell = self.output_sheet.cell(row=1, column=col)
                    cell.value = first_header[key]
                    cell.font = header_font
                    cell.alignment = center_alignment

                cell = self.output_sheet["A2"]
                cell.value = first_header["model_lower"]
                cell.font = header_font
                cell.alignment = center_alignment

                cell = self.output_sheet["B2"]
                cell.value = first_header["variant"]
                cell.font = header_font
                cell.alignment = center_alignment

                self.output_sheet.merge_cells("C2:D2")
                cell = self.output_sheet["C2"]
                cell.value = first_header["merged1"]
                cell.font = header_font
                cell.alignment = center_alignment

                cell = self.output_sheet["E2"]
                cell.value = first_header["single1"]
                cell.font = header_font
                cell.alignment = center_alignment

                self.output_sheet.merge_cells("F2:G2")
                cell = self.output_sheet["F2"]
                cell.value = first_header["merged2"]
                cell.font = header_font
                cell.alignment = center_alignment

                for col, key in enumerate(["single2", "single3", "single4"], 8):
                    cell = self.output_sheet.cell(row=2, column=col)
                    cell.value = first_header[key]
                    cell.font = header_font
                    cell.alignment = center_alignment

                for col_idx, extra_data in enumerate(first_header["extra_header_row1"]):
                    cell = self.output_sheet.cell(row=1, column=11 + col_idx)
                    cell.value = extra_data["value"]
                    copy_cell_format(extra_data["cell"], cell)
                    cell.alignment = center_alignment

                for merged_range in first_header["merged_ranges_row1"]:
                    start_col = merged_range["start_col"]
                    end_col = merged_range["end_col"]
                    self.output_sheet.merge_cells(start_row=1, start_column=start_col, end_row=1, end_column=end_col)
                    cell = self.output_sheet.cell(row=1, column=start_col)
                    cell.value = merged_range["value"]
                    copy_cell_format(extra_data["cell"], cell)
                    cell.alignment = center_alignment

                for col_idx, extra_data in enumerate(first_header["extra_header_row2"]):
                    cell = self.output_sheet.cell(row=2, column=11 + col_idx)
                    cell.value = extra_data["value"]
                    copy_cell_format(extra_data["cell"], cell)
                    cell.alignment = center_alignment

            self.current_row = 3

            for _, result_group in grouped_results.items():
                for result in result_group:
                    row_data = result["row"]
                    issues = result.get("issues", [])
                    self.output_sheet.row_dimensions[self.current_row].height = 20
                    for col, key in enumerate(["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"], 1):
                        cell = self.output_sheet.cell(row=self.current_row, column=col)
                        cell_data = row_data[key]
                        cell.value = cell_data["value"]
                        try:
                            copy_cell_format(cell_data["cell"], cell)
                            cell.alignment = center_alignment
                            if issues:
                                logger.info(
                                    f"[{result['input_file']}/{result['input_sheet']}] 行{result['input_row']} "
                                    f"标黄，原因：{'；'.join(issues)}"
                                )
                                apply_yellow_fill(cell)
                            else:
                                logger.debug(
                                    f"[{result['input_file']}/{result['input_sheet']}] 行{result['input_row']} "
                                    f"未标黄，无问题"
                                )
                        except Exception as e:
                            logger.error(
                                f"[{result['input_file']}/{result['input_sheet']}] 行{result['input_row']} "
                                f"列{key} 写入格式失败，值='{cell_data['value']}'，错误：{str(e)}"
                            )
                            cell.font = Font(name="Microsoft YaHei", size=12, strike=False)
                            cell.alignment = center_alignment

                    for col_idx, extra_data in enumerate(row_data.get("extra", [])):
                        cell = self.output_sheet.cell(row=self.current_row, column=11 + col_idx)
                        cell.value = extra_data["value"]
                        try:
                            copy_cell_format(extra_data["cell"], cell)
                            cell.alignment = center_alignment
                            if issues:
                                apply_yellow_fill(cell)
                        except Exception as e:
                            logger.error(
                                f"[{result['input_file']}/{result['input_sheet']}] 行{result['input_row']} "
                                f"额外列{col_idx+11} 写入格式失败，值='{extra_data['value']}'，错误：{str(e)}"
                            )
                            cell.font = Font(name="Microsoft YaHei", size=12, strike=False)
                            cell.alignment = center_alignment

                    self.current_row += 1

            output_filename = OUTPUT_FILENAME_TEMPLATE.format(model=self.model, controller=self.controller)
            output_path = OUTPUT_DIR / output_filename
            self.output_workbook.save(output_path)
            logger.info(f"输出文件保存至：{output_path}")
        except Exception as e:
            logger.error(f"写入输出文件时出错：{str(e)}")
            raise

    def fill_template_sheet(self, results: List[Dict]):
        """将ConfigWord Sheet的内容填充到模板文件的‘工作表’Sheet中"""
        try:
            template_file = self.prepare_template_file()
            if not template_file:
                logger.warning("未找到模板文件，跳过填充工作表")
                return

            template_workbook = openpyxl.load_workbook(template_file)
            if "工作表" not in template_workbook.sheetnames:
                logger.error(f"模板文件{template_file.name}中未找到‘工作表’Sheet")
                raise ValueError("模板文件中缺少‘工作表’Sheet")

            target_sheet = template_workbook["工作表"]

            target_sheet.delete_rows(1, target_sheet.max_row)
            target_sheet.delete_cols(1, target_sheet.max_column)
            logger.info(f"已清空模板文件{template_file.name}的‘工作表’Sheet")

            target_sheet.column_dimensions["A"].width = 25
            target_sheet.column_dimensions["B"].width = 25
            for col in range(3, 11):
                target_sheet.column_dimensions[get_column_letter(col)].width = 10
            for col in range(11, 11 + self.max_extra_cols):
                target_sheet.column_dimensions[get_column_letter(col)].width = 20

            target_sheet.row_dimensions[1].height = 30
            target_sheet.row_dimensions[2].height = 30

            header_font = Font(name="Microsoft YaHei", size=14)
            center_alignment = Alignment(horizontal="center", vertical="center")

            grouped_results = {}
            for result in results:
                key = (result["input_file"], result["input_sheet"])
                if key not in grouped_results:
                    grouped_results[key] = []
                grouped_results[key].append(result)

            if results:
                first_header = results[0]["header"]
                target_sheet.merge_cells("A1:B1")
                cell = target_sheet["A1"]
                cell.value = first_header["model_upper"]
                cell.font = header_font
                cell.alignment = center_alignment

                for col, key in enumerate(["byte1", "byte2", "byte3", "byte4", "byte5", "byte6", "byte7", "byte8"], 3):
                    cell = target_sheet.cell(row=1, column=col)
                    cell.value = first_header[key]
                    cell.font = header_font
                    cell.alignment = center_alignment

                cell = target_sheet["A2"]
                cell.value = first_header["model_lower"]
                cell.font = header_font
                cell.alignment = center_alignment

                cell = target_sheet["B2"]
                cell.value = first_header["variant"]
                cell.font = header_font
                cell.alignment = center_alignment

                target_sheet.merge_cells("C2:D2")
                cell = target_sheet["C2"]
                cell.value = first_header["merged1"]
                cell.font = header_font
                cell.alignment = center_alignment

                cell = target_sheet["E2"]
                cell.value = first_header["single1"]
                cell.font = header_font
                cell.alignment = center_alignment

                target_sheet.merge_cells("F2:G2")
                cell = target_sheet["F2"]
                cell.value = first_header["merged2"]
                cell.font = header_font
                cell.alignment = center_alignment

                for col, key in enumerate(["single2", "single3", "single4"], 8):
                    cell = target_sheet.cell(row=2, column=col)
                    cell.value = first_header[key]
                    cell.font = header_font
                    cell.alignment = center_alignment

                for col_idx, extra_data in enumerate(first_header["extra_header_row1"]):
                    cell = target_sheet.cell(row=1, column=11 + col_idx)
                    cell.value = extra_data["value"]
                    copy_cell_format(extra_data["cell"], cell)
                    cell.alignment = center_alignment

                for merged_range in first_header["merged_ranges_row1"]:
                    start_col = merged_range["start_col"]
                    end_col = merged_range["end_col"]
                    target_sheet.merge_cells(start_row=1, start_column=start_col, end_row=1, end_column=end_col)
                    cell = target_sheet.cell(row=1, column=start_col)
                    cell.value = merged_range["value"]
                    copy_cell_format(extra_data["cell"], cell)
                    cell.alignment = center_alignment

                for col_idx, extra_data in enumerate(first_header["extra_header_row2"]):
                    cell = target_sheet.cell(row=2, column=11 + col_idx)
                    cell.value = extra_data["value"]
                    copy_cell_format(extra_data["cell"], cell)
                    cell.alignment = center_alignment

            current_row = 3
            for _, result_group in grouped_results.items():
                for result in result_group:
                    row_data = result["row"]
                    issues = result.get("issues", [])
                    target_sheet.row_dimensions[current_row].height = 20
                    for col, key in enumerate(["A", "B", "C", "D", "E", "F", "G", "H", "I", "J"], 1):
                        cell = target_sheet.cell(row=current_row, column=col)
                        cell_data = row_data[key]
                        cell.value = cell_data["value"]
                        try:
                            copy_cell_format(cell_data["cell"], cell)
                            cell.alignment = center_alignment
                            if issues:
                                logger.info(
                                    f"[{result['input_file']}/{result['input_sheet']}] 行{result['input_row']} "
                                    f"标黄（模板文件），原因：{'；'.join(issues)}"
                                )
                                apply_yellow_fill(cell)
                            else:
                                logger.debug(
                                    f"[{result['input_file']}/{result['input_sheet']}] 行{result['input_row']} "
                                    f"未标黄（模板文件），无问题"
                                )
                        except Exception as e:
                            logger.error(
                                f"[{result['input_file']}/{result['input_sheet']}] 行{result['input_row']} "
                                f"列{key} 写入格式失败（模板文件），值='{cell_data['value']}'，错误：{str(e)}"
                            )
                            cell.font = Font(name="Microsoft YaHei", size=12, strike=False)
                            cell.alignment = center_alignment

                    for col_idx, extra_data in enumerate(row_data.get("extra", [])):
                        cell = target_sheet.cell(row=current_row, column=11 + col_idx)
                        cell.value = extra_data["value"]
                        try:
                            copy_cell_format(extra_data["cell"], cell)
                            cell.alignment = center_alignment
                            if issues:
                                apply_yellow_fill(cell)
                        except Exception as e:
                            logger.error(
                                f"[{result['input_file']}/{result['input_sheet']}] 行{result['input_row']} "
                                f"额外列{col_idx+11} 写入格式失败（模板文件），值='{extra_data['value']}'，错误：{str(e)}"
                            )
                            cell.font = Font(name="Microsoft YaHei", size=12, strike=False)
                            cell.alignment = center_alignment

                    current_row += 1

            template_workbook.save(template_file)
            logger.info(f"模板文件‘工作表’Sheet已更新：{template_file}")
        except Exception as e:
            logger.error(f"填充模板文件‘工作表’Sheet时出错：{str(e)}")
            raise

    def run(self):
        """执行整个处理流程"""
        try:
            files = self.find_config_files()
            if not files:
                raise ValueError("未找到任何配置文件")
            all_results = []
            for file in files:
                results = self.process_sheets(file)
                all_results.extend(results)
            if not all_results:
                logger.warning("未找到任何符合条件的配置信息")
            else:
                self.write_output(all_results)
                self.fill_template_sheet(all_results)
        except Exception as e:
            logger.error(f"程序执行失败：{str(e)}")
            raise
        finally:
            self.warning_collector.print_warnings()
            logger.removeHandler(self.warning_collector)