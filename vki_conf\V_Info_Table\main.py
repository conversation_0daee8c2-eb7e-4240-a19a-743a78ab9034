# main.py
from excel_to_sqlite import excel_to_sqlite
from create_new_table import create_new_table
from merge_to_excel import merge_new_vehicle_table_to_excel
from highlight_non_numeric import highlight_non_numeric_in_vehicle_columns
from config import INPUT_EXCEL_PATH, DATABASE_PATH

def main():
    """主程序：执行从 Excel 处理到最终输出的完整流程"""
    try:
        # 步骤 1：将输入 Excel 数据转换为 SQLite 数据库
        print("开始将 Excel 数据转换为 SQLite 数据库...")
        excel_to_sqlite(INPUT_EXCEL_PATH, DATABASE_PATH)
        print("Excel 数据已成功转换为 SQLite 数据库\n")

        # 步骤 2：创建 new_vehicle_table
        print("开始创建 new_vehicle_table...")
        create_new_table(DATABASE_PATH, "new_vehicle_table")
        print("new_vehicle_table 已创建\n")

        # 步骤 3：将 new_vehicle_table 合并到基于模板的输出 Excel
        print("开始将 new_vehicle_table 合并到输出 Excel...")
        output_excel_path = merge_new_vehicle_table_to_excel(DATABASE_PATH, input_file=INPUT_EXCEL_PATH)
        print("new_vehicle_table 已合并到输出 Excel\n")

        # 步骤 4：高亮非数字值
        print("开始高亮车型列中的非数字值...")
        highlight_non_numeric_in_vehicle_columns(output_excel_path, DATABASE_PATH)
        print("非数字值高亮完成\n")

        print(f"所有步骤已成功完成！输出文件: {output_excel_path}")

    except Exception as e:
        print(f"程序执行出错: {e}")
        raise

if __name__ == "__main__":
    main()