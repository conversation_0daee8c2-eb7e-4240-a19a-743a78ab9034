import sys
import os
import shutil
import subprocess
import fileinput
import logging
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QTextEdit, QMessageBox, QGroupBox, QProgressBar, QLineEdit, QComboBox
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QCoreApplication
from PyQt5.QtGui import QFont


class VKIWorker(QThread):
    """VKI工作线程"""
    progress = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, operation, core_instance, model="", controller=""):
        super().__init__()
        self.operation = operation
        self.core = core_instance
        self.model = model
        self.controller = controller
        self.is_running = False

    def run(self):
        self.is_running = True
        try:
            self.progress.emit(f"线程开始执行: {self.operation}")
            
            if self.operation == "v_info_table":
                result = self._run_v_info_table()
                if result:
                    self.finished.emit(True, "关键信息表处理完成")
                else:
                    self.finished.emit(False, "关键信息表处理失败")
            elif self.operation == "v_conf_table":
                result = self._run_v_conf_table()
                if result:
                    self.finished.emit(True, "配置字表处理完成")
                else:
                    self.finished.emit(False, "配置字表处理失败")

                    
        except Exception as e:
            self.progress.emit(f"线程异常: {str(e)}")
            self.finished.emit(False, f"处理失败: {str(e)}")
        finally:
            self.is_running = False
            self.progress.emit("线程结束")
    
    def _run_v_info_table(self):
        """在线程中运行关键信息表处理"""
        try:
            self.progress.emit("开始处理关键信息表...")
            
            if not os.path.exists(self.core.v_info_input_dir):
                self.progress.emit("错误: 关键信息表输入文件夹不存在")
                return False
            
            info_files = [f for f in os.listdir(self.core.v_info_input_dir) if "关键信息" in f]
            if not info_files:
                self.progress.emit("错误: 没有找到包含'关键信息'的文件")
                return False
            
            output_subfolder = self.core._create_timestamped_folder(self.core.v_info_output_dir, "v_info_")
            self.core._clear_work_directories()
            
            for filename in info_files:
                self.progress.emit(f"处理文件: {filename}")
                
                src_path = os.path.join(self.core.v_info_input_dir, filename)
                dst_path = os.path.join(self.core.v_info_work_dir, filename)
                shutil.copy2(src_path, dst_path)
                
                config_path = os.path.join(self.core.base_dir, "V_Info_Table", "config.py")
                if os.path.exists(config_path):
                    self.core.update_config_file(config_path, filename)
                
                self.progress.emit(f"运行V_Info_Table处理: {filename}")
                result = subprocess.run([sys.executable, "main.py"], 
                                      cwd=os.path.join(self.core.base_dir, "V_Info_Table"), 
                                      capture_output=True, text=True, 
                                      encoding='utf-8', errors='replace', timeout=300)
                
                if result.returncode != 0:
                    self.progress.emit(f"处理失败: {filename}")
                    continue
                
                if os.path.exists(self.core.v_info_work_output_dir):
                    for output_file in os.listdir(self.core.v_info_work_output_dir):
                        src = os.path.join(self.core.v_info_work_output_dir, output_file)
                        dst = os.path.join(output_subfolder, output_file)
                        if os.path.isfile(src):
                            shutil.move(src, dst)
                
                self.core._clear_work_directories()
            
            self.core._move_input_to_history(self.core.v_info_input_dir, self.core.v_info_input_dir)
            return True
            
        except Exception as e:
            self.progress.emit(f"处理失败: {str(e)}")
            return False
    
    def _run_v_conf_table(self):
        """在线程中运行配置字表处理"""
        try:
            self.progress.emit("开始处理配置字表...")
            
            if not os.path.exists(self.core.v_conf_input_dir):
                self.progress.emit("错误: 配置字表输入文件夹不存在")
                return False
            
            conf_files = [f for f in os.listdir(self.core.v_conf_input_dir) if "配置字" in f]
            if not conf_files:
                self.progress.emit("错误: 没有找到包含'配置字'的文件")
                return False
            
            output_subfolder = self.core._create_timestamped_folder(self.core.v_conf_output_dir, "v_conf_")
            
            if os.path.exists(self.core.v_conf_work_input_dir):
                for item in os.listdir(self.core.v_conf_work_input_dir):
                    os.remove(os.path.join(self.core.v_conf_work_input_dir, item))
            
            if os.path.exists(self.core.v_conf_work_output_dir):
                for item in os.listdir(self.core.v_conf_work_output_dir):
                    os.remove(os.path.join(self.core.v_conf_work_output_dir, item))
            
            for filename in conf_files:
                src_path = os.path.join(self.core.v_conf_input_dir, filename)
                dst_path = os.path.join(self.core.v_conf_work_input_dir, filename)
                shutil.copy2(src_path, dst_path)
            
            v_conf_src_path = os.path.join(self.core.base_dir, "V_Conf_Table", "src")
            sys.path.insert(0, v_conf_src_path)
            
            try:
                from excel_processor import ExcelProcessor
                from config import ensure_directories
                
                ensure_directories()
                processor = ExcelProcessor(self.model, self.controller)
                processor.run()
                
            finally:
                if v_conf_src_path in sys.path:
                    sys.path.remove(v_conf_src_path)
            
            if os.path.exists(self.core.v_conf_work_output_dir):
                for output_file in os.listdir(self.core.v_conf_work_output_dir):
                    src = os.path.join(self.core.v_conf_work_output_dir, output_file)
                    dst = os.path.join(output_subfolder, output_file)
                    if os.path.isfile(src):
                        shutil.copy2(src, dst)
            
            self.core._move_input_to_history(self.core.v_conf_input_dir, self.core.v_conf_input_dir)
            return True
            
        except Exception as e:
            self.progress.emit(f"处理失败: {str(e)}")
            return False


class VKICore:
    """VKI核心功能类"""
    
    def __init__(self, gui_instance=None):
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 关键信息表独立路径
        self.v_info_input_dir = os.path.join(self.base_dir, "v_info_input")
        self.v_info_output_dir = os.path.join(self.base_dir, "v_info_output")
        self.v_info_work_dir = os.path.join(self.base_dir, "V_Info_Table", "data")
        self.v_info_work_output_dir = os.path.join(self.base_dir, "V_Info_Table", "outputs")
        
        # 配置字表独立路径
        self.v_conf_input_dir = os.path.join(self.base_dir, "v_conf_input")
        self.v_conf_output_dir = os.path.join(self.base_dir, "v_conf_output")
        self.v_conf_work_input_dir = os.path.join(self.base_dir, "V_Conf_Table", "data", "input")
        self.v_conf_work_output_dir = os.path.join(self.base_dir, "V_Conf_Table", "data", "template_output")
        
        # 日志目录
        self.log_dir = os.path.join(self.base_dir, "logs")
        
        self.gui_instance = gui_instance
        self.output_callback = None
        self.logger = None
        self._ensure_directories()
        self._setup_logger()
    
    def set_output_callback(self, callback):
        """设置输出回调函数"""
        self.output_callback = callback
    
    def _print(self, message):
        """输出消息"""
        print(message)
        if self.logger:
            self.logger.info(message)
        if self.output_callback:
            try:
                self.output_callback(message)
            except:
                pass
    
    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        directories = [
            self.v_info_input_dir, self.v_info_output_dir, self.v_info_work_dir, self.v_info_work_output_dir,
            self.v_conf_input_dir, self.v_conf_output_dir, self.v_conf_work_input_dir, self.v_conf_work_output_dir,
            self.log_dir
        ]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def _setup_logger(self):
        """设置日志记录器"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(self.log_dir, f"vki_gui_{timestamp}.log")
        
        self.logger = logging.getLogger('VKI_GUI')
        self.logger.setLevel(logging.INFO)
        
        # 清除已有的处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
    
    def _create_timestamped_folder(self, base_path, prefix=""):
        """创建带时间戳的文件夹"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            folder_name = f"{prefix}{timestamp}" if prefix else timestamp
            folder_path = os.path.join(base_path, folder_name)
            self._print(f"正在创建文件夹: {folder_path}")
            os.makedirs(folder_path, exist_ok=True)
            self._print(f"文件夹创建成功: {folder_path}")
            return folder_path
        except Exception as e:
            self._print(f"创建文件夹失败: {str(e)}")
            raise
    
    def _move_input_to_history(self, source_dir, target_base_dir):
        """将输入文件移动到历史文件夹"""
        try:
            self._print(f"开始移动输入文件: {source_dir}")
            
            if not os.path.exists(source_dir):
                self._print(f"输入文件夹不存在: {source_dir}")
                return
            
            self._print("正在获取文件列表...")
            files = []
            try:
                files = os.listdir(source_dir)
                self._print(f"找到 {len(files)} 个文件/文件夹")
            except Exception as e:
                self._print(f"获取文件列表失败: {str(e)}")
                return
            
            if not files:
                self._print("输入文件夹为空")
                return
            
            self._print("正在创建历史文件夹...")
            try:
                history_folder = self._create_timestamped_folder(target_base_dir, "history_")
                self._print(f"创建历史文件夹: {history_folder}")
            except Exception as e:
                self._print(f"创建历史文件夹失败: {str(e)}")
                return
            
            moved_count = 0
            for i, item in enumerate(files):
                try:
                    self._print(f"正在处理文件 {i+1}/{len(files)}: {item}")
                    src_path = os.path.join(source_dir, item)
                    dst_path = os.path.join(history_folder, item)
                    
                    if os.path.isfile(src_path):
                        self._print(f"移动文件: {src_path} -> {dst_path}")
                        shutil.move(src_path, dst_path)
                        moved_count += 1
                        self._print(f"成功移动文件: {item}")
                    else:
                        self._print(f"跳过非文件: {item}")
                except Exception as e:
                    self._print(f"移动文件 {item} 失败: {str(e)}")
                    continue
            
            self._print(f"文件移动完成，共移动 {moved_count} 个文件")
            
        except Exception as e:
            self._print(f"移动输入文件到历史文件夹失败: {str(e)}")
            import traceback
            self._print(f"详细错误信息: {traceback.format_exc()}")
    
    def _clear_work_directories(self):
        """清空工作目录"""
        # 清空data目录
        if os.path.exists(self.v_info_work_dir):
            for item in os.listdir(self.v_info_work_dir):
                item_path = os.path.join(self.v_info_work_dir, item)
                if os.path.isfile(item_path):
                    os.remove(item_path)
        
        # 清空outputs目录
        if os.path.exists(self.v_info_work_output_dir):
            for item in os.listdir(self.v_info_work_output_dir):
                item_path = os.path.join(self.v_info_work_output_dir, item)
                if os.path.isfile(item_path):
                    os.remove(item_path)
    
    def update_config_file(self, config_path, new_filename):
        """更新V_Info_Table/config.py中的INPUT_EXCEL_PATH"""
        try:
            with fileinput.FileInput(config_path, inplace=True, encoding='utf-8') as file:
                for line in file:
                    if line.strip().startswith("INPUT_EXCEL_PATH"):
                        print(f'INPUT_EXCEL_PATH = "data/{new_filename}"')
                    else:
                        print(line, end='')
        except Exception as e:
            self._print(f"更新配置文件失败: {str(e)}")
    
    def run_v_info_table(self):
        """处理关键信息表"""
        try:
            self._print("开始处理关键信息表...")
            
            # 检查输入文件
            if not os.path.exists(self.v_info_input_dir):
                self._print("错误: 关键信息表输入文件夹不存在")
                return False
            
            info_files = [f for f in os.listdir(self.v_info_input_dir) if "关键信息" in f]
            if not info_files:
                self._print("错误: 关键信息表输入文件夹中没有包含'关键信息'的文件")
                return False
            
            # 创建输出子文件夹
            output_subfolder = self._create_timestamped_folder(self.v_info_output_dir, "v_info_")
            
            # 先清空V_Info_Table/data和outputs
            self._print("清空工作目录...")
            self._clear_work_directories()
            
            # 逐个处理每个文件
            for filename in info_files:
                self._print(f"处理文件: {filename}")
                
                # 复制文件到V_Info_Table/data
                src_path = os.path.join(self.v_info_input_dir, filename)
                dst_path = os.path.join(self.v_info_work_dir, filename)
                shutil.copy2(src_path, dst_path)
                
                # 更新配置文件
                config_path = os.path.join(self.base_dir, "V_Info_Table", "config.py")
                if os.path.exists(config_path):
                    self.update_config_file(config_path, filename)
                
                # 运行V_Info_Table
                self._print(f"运行V_Info_Table处理: {filename}")
                try:
                    # 设置环境变量解决编码问题
                    env = os.environ.copy()
                    env['PYTHONIOENCODING'] = 'utf-8'
                    env['PYTHONLEGACYWINDOWSSTDIO'] = '1'
                    
                    result = subprocess.run([sys.executable, "main.py"], 
                                          cwd=os.path.join(self.base_dir, "V_Info_Table"), 
                                          capture_output=True, text=True, 
                                          encoding='utf-8', errors='replace',
                                          timeout=300, env=env,
                                          creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
                except subprocess.TimeoutExpired:
                    self._print(f"处理超时: {filename}")
                    continue
                except Exception as e:
                    self._print(f"运行子进程失败: {str(e)}")
                    continue
                
                if result.stdout:
                    # 清理输出信息，去除乱码
                    clean_stdout = result.stdout.replace('\x00', '').strip()
                    if clean_stdout:
                        self._print(f"V_Info_Table输出: {clean_stdout[:500]}...")  # 限制输出长度
                if result.stderr:
                    clean_stderr = result.stderr.replace('\x00', '').strip()
                    if clean_stderr:
                        self._print(f"V_Info_Table错误: {clean_stderr[:500]}...")
                
                if result.returncode != 0:
                    self._print(f"处理失败，返回码: {result.returncode}")
                    continue
                
                # 将outputs里的文件移动到输出子文件夹
                if os.path.exists(self.v_info_work_output_dir):
                    for output_file in os.listdir(self.v_info_work_output_dir):
                        src = os.path.join(self.v_info_work_output_dir, output_file)
                        dst = os.path.join(output_subfolder, output_file)
                        if os.path.isfile(src):
                            shutil.move(src, dst)
                            self._print(f"输出文件已移动: {dst}")
                
                # 清理工作目录中的文件，为下一个文件做准备
                self._clear_work_directories()
            
            # 移动输入文件到历史文件夹
            self._print("开始移动输入文件到历史文件夹...")
            self._move_input_to_history(self.v_info_input_dir, self.v_info_input_dir)
            self._print("输入文件移动完成")
            
            self._print("关键信息表处理完成")
            # 强制刷新GUI
            QCoreApplication.processEvents()
            return True
            
        except Exception as e:
            self._print(f"处理关键信息表时出错: {str(e)}")
            return False
    
    def get_user_inputs(self):
        """获取用户输入的参数"""
        if self.gui_instance and hasattr(self.gui_instance, 'model_input') and hasattr(self.gui_instance, 'controller_input'):
            model = self.gui_instance.model_input.text().strip()
            controller = self.gui_instance.controller_input.currentText().strip()
            
            # 验证输入
            if not model:
                model = "HC2"
            if not controller:
                controller = "D2"
            
            return model, controller
        return "HC2", "D2"
    
    def run_v_conf_table(self):
        """处理配置字表"""
        try:
            self._print("开始处理配置字表...")
            
            # 获取用户输入参数
            model, controller = self.get_user_inputs()
            self._print(f"使用参数: 车型={model}, 控制器={controller}")
            
            # 检查输入文件
            if not os.path.exists(self.v_conf_input_dir):
                self._print("错误: 配置字表输入文件夹不存在")
                return False
            
            conf_files = [f for f in os.listdir(self.v_conf_input_dir) if "配置字" in f]
            if not conf_files:
                self._print("错误: 配置字表输入文件夹中没有包含'配置字'的文件")
                return False
            
            # 创建输出子文件夹
            output_subfolder = self._create_timestamped_folder(self.v_conf_output_dir, "v_conf_")
            
            # 先清空V_Conf_Table/data/input和template_output
            self._print("清空工作目录...")
            if os.path.exists(self.v_conf_work_input_dir):
                for item in os.listdir(self.v_conf_work_input_dir):
                    item_path = os.path.join(self.v_conf_work_input_dir, item)
                    if os.path.isfile(item_path):
                        os.remove(item_path)
            
            if os.path.exists(self.v_conf_work_output_dir):
                for item in os.listdir(self.v_conf_work_output_dir):
                    item_path = os.path.join(self.v_conf_work_output_dir, item)
                    if os.path.isfile(item_path):
                        os.remove(item_path)
            
            # 复制配置字文件到V_Conf_Table/data/input
            for filename in conf_files:
                src_path = os.path.join(self.v_conf_input_dir, filename)
                dst_path = os.path.join(self.v_conf_work_input_dir, filename)
                shutil.copy2(src_path, dst_path)
                self._print(f"已复制配置字文件: {filename}")
            
            # 运行V_Conf_Table（使用用户输入参数）
            self._print("运行V_Conf_Table处理...")
            # 创建一个临时的main.py来使用用户输入的参数
            temp_main_content = f'''from excel_processor import ExcelProcessor
from config import ensure_directories
from utils import setup_logging

logger = setup_logging()

def main():
    """主程序入口"""
    try:
        # 确保目录存在
        ensure_directories()
        
        # 使用用户输入的参数
        model = "{model}"
        controller = "{controller}"
        
        logger.info(f"开始处理：车型={{model}}, 控制器={{controller}}")
        
        # 创建处理器并运行
        processor = ExcelProcessor(model, controller)
        processor.run()
        
        logger.info("程序执行完成")
    except Exception as e:
        logger.error(f"程序运行失败：{{str(e)}}")
        raise

if __name__ == "__main__":
    main()
'''
            
            # 备份原始main.py
            original_main = os.path.join(self.base_dir, "V_Conf_Table", "src", "main.py")
            backup_main = os.path.join(self.base_dir, "V_Conf_Table", "src", "main_backup.py")
            if os.path.exists(original_main):
                shutil.copy2(original_main, backup_main)
            
            # 写入临时main.py
            with open(original_main, 'w', encoding='utf-8') as f:
                f.write(temp_main_content)
            
            try:
                # 设置环境变量解决编码问题
                env = os.environ.copy()
                env['PYTHONIOENCODING'] = 'utf-8'
                env['PYTHONLEGACYWINDOWSSTDIO'] = '1'
                
                result = subprocess.run([sys.executable, "main.py"], 
                                      cwd=os.path.join(self.base_dir, "V_Conf_Table", "src"), 
                                      capture_output=True, text=True, 
                                      encoding='utf-8', errors='replace',
                                      timeout=300, env=env,
                                      creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0)
                
                if result.stdout:
                    clean_stdout = result.stdout.replace('\x00', '').strip()
                    if clean_stdout:
                        self._print(f"V_Conf_Table输出: {clean_stdout[:500]}...")
                if result.stderr:
                    clean_stderr = result.stderr.replace('\x00', '').strip()
                    if clean_stderr:
                        self._print(f"V_Conf_Table错误: {clean_stderr[:500]}...")
                
                if result.returncode != 0:
                    self._print(f"处理失败，返回码: {result.returncode}")
                    return False
            finally:
                # 恢复原始main.py
                if os.path.exists(backup_main):
                    shutil.move(backup_main, original_main)
            
            # 将template_output里的内容复制到输出子文件夹
            if os.path.exists(self.v_conf_work_output_dir):
                for output_file in os.listdir(self.v_conf_work_output_dir):
                    src = os.path.join(self.v_conf_work_output_dir, output_file)
                    dst = os.path.join(output_subfolder, output_file)
                    if os.path.isfile(src):
                        shutil.copy2(src, dst)
                        self._print(f"输出文件已复制: {dst}")
            
            # 移动输入文件到历史文件夹
            self._move_input_to_history(self.v_conf_input_dir, self.v_conf_input_dir)
            
            self._print("配置字表处理完成")
            return True
            
        except Exception as e:
            self._print(f"处理配置字表时出错: {str(e)}")
            return False
    
    def open_folder(self, path):
        """打开文件夹"""
        if not os.path.exists(path):
            os.makedirs(path)
        if os.name == 'nt':  # Windows
            os.startfile(path)
        else:
            QMessageBox.information(None, "提示", f"请手动打开: {path}")
    
    def get_latest_output_folder(self, base_output_dir):
        """获取最新的输出子文件夹"""
        if not os.path.exists(base_output_dir):
            return base_output_dir
        
        subfolders = [f for f in os.listdir(base_output_dir) 
                     if os.path.isdir(os.path.join(base_output_dir, f))]
        if not subfolders:
            return base_output_dir
        
        # 按时间戳排序，返回最新的
        subfolders.sort(reverse=True)
        return os.path.join(base_output_dir, subfolders[0])


class VKIGUI(QMainWindow):
    def __init__(self, on_home=None, on_back=None):
        super().__init__()
        self.on_home = on_home
        self.on_back = on_back
        self.core = VKICore(self)
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("关键信息表和配置字表处理工具")
        self.setGeometry(100, 100, 1200, 800)
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 顶部导航按钮
        nav_container = QWidget()
        nav_layout = QHBoxLayout(nav_container)
        nav_layout.setContentsMargins(0, 0, 0, 10)
        
        # 返回上一级按钮（橙色）
        back_btn = QPushButton("返回上一级")
        back_btn.setFont(QFont("Arial", 11))
        back_btn.setMaximumSize(120, 36)
        if self.on_back:
            back_btn.clicked.connect(self.on_back)
        else:
            back_btn.clicked.connect(self.close)
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        nav_layout.addWidget(back_btn)
        
        # 返回主页按钮（主色蓝色）
        home_btn = QPushButton("返回主页")
        home_btn.setFont(QFont("Arial", 11))
        home_btn.setMaximumSize(120, 36)
        if self.on_home:
            home_btn.clicked.connect(self.on_home)
        else:
            home_btn.clicked.connect(self.close)
        home_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 5px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        nav_layout.addWidget(home_btn)
        nav_layout.addStretch()
        main_layout.addWidget(nav_container)
        
        # 创建标题
        title_label = QLabel("关键信息表和配置字表处理工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin: 10px 0;")
        main_layout.addWidget(title_label)
        
        # 创建左右分布的工具区域
        tools_container = QWidget()
        tools_container.setMinimumHeight(350)
        tools_layout = QHBoxLayout(tools_container)
        tools_layout.setSpacing(20)
        
        # 左侧：关键信息表工具（占一半宽度）
        left_group = self.create_v_info_group()
        left_group.setMinimumHeight(330)
        tools_layout.addWidget(left_group, 1)
        
        # 右侧：配置字表工具（占一半宽度）
        right_group = self.create_v_conf_group()
        right_group.setMinimumHeight(330)
        tools_layout.addWidget(right_group, 1)
        
        main_layout.addWidget(tools_container)
        
        # 同时运行按钮
        both_btn = QPushButton("同时运行两个工具")
        both_btn.setFont(QFont("Arial", 13, QFont.Bold))
        both_btn.setFixedSize(220, 50)
        both_btn.clicked.connect(self.process_both)
        both_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 13pt;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        # 居中显示同时运行按钮
        both_container = QWidget()
        both_container.setMaximumHeight(60)
        both_layout = QHBoxLayout(both_container)
        both_layout.setContentsMargins(0, 10, 0, 10)
        both_layout.addStretch()
        both_layout.addWidget(both_btn)
        both_layout.addStretch()
        main_layout.addWidget(both_container)
        
        # 创建状态显示区域
        self.create_status_area(main_layout)
        
        # 设置整体样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QWidget {
                background-color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin: 10px 0;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
    
    def create_v_info_group(self):
        """创建关键信息表工具组"""
        group = QGroupBox("关键信息表处理工具")
        group.setFont(QFont("Arial", 11, QFont.Bold))
        layout = QVBoxLayout(group)
        
        # 上方适当间距
        layout.addStretch(1)
        
        # 文件夹操作
        folder_layout = QHBoxLayout()
        
        input_btn = QPushButton("打开输入文件夹")
        input_btn.setFont(QFont("Arial", 10))
        input_btn.setFixedSize(150, 38)
        input_btn.clicked.connect(self.open_v_info_input_folder)
        input_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        output_btn = QPushButton("打开输出文件夹")
        output_btn.setFont(QFont("Arial", 10))
        output_btn.setFixedSize(150, 38)
        output_btn.clicked.connect(self.open_v_info_output_folder)
        output_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        folder_layout.addWidget(input_btn)
        folder_layout.addWidget(output_btn)
        folder_layout.setSpacing(15)
        layout.addLayout(folder_layout)
        
        # 增加文件夹按钮与运行按钮之间的间距
        layout.addSpacing(25)
        
        # 运行按钮
        run_btn = QPushButton("处理关键信息表")
        run_btn.setFont(QFont("Arial", 12, QFont.Bold))
        run_btn.setFixedSize(180, 50)
        run_btn.clicked.connect(self.process_v_info_table)
        run_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        # 居中放置按钮
        btn_container = QWidget()
        btn_layout = QHBoxLayout(btn_container)
        btn_layout.addStretch()
        btn_layout.addWidget(run_btn)
        btn_layout.addStretch()
        layout.addWidget(btn_container)
        
        # 下方适当间距，使按钮在区域内居中
        layout.addStretch(1)
        
        return group
    
    def create_v_conf_group(self):
        """创建配置字表工具组"""
        group = QGroupBox("配置字表处理工具")
        group.setFont(QFont("Arial", 11, QFont.Bold))
        layout = QVBoxLayout(group)
        
        # 参数设置（去掉标题）
        
        param_layout = QGridLayout()
        param_layout.setSpacing(12)
        param_layout.setContentsMargins(10, 10, 10, 15)
        
        # 车型代号输入
        model_label = QLabel("车型代号:")
        model_label.setFont(QFont("Arial", 10))
        self.model_input = QLineEdit()
        self.model_input.setPlaceholderText("请输入车型代号，如：HC2")
        self.model_input.setText("")
        self.model_input.setFont(QFont("Arial", 10))
        self.model_input.setMinimumHeight(28)
        self.model_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 6px;
                background-color: white;
                font-size: 10pt;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        # 控制器名称选择
        controller_label = QLabel("控制器名称:")
        controller_label.setFont(QFont("Arial", 10))
        self.controller_input = QComboBox()
        self.controller_input.addItems(["D2", "D3", "域控", "OneBox"])
        self.controller_input.setCurrentText("D2")
        self.controller_input.setFont(QFont("Arial", 10))
        self.controller_input.setMinimumHeight(28)
        self.controller_input.setStyleSheet("""
            QComboBox {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 6px;
                background-color: white;
                font-size: 10pt;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        
        param_layout.addWidget(model_label, 0, 0)
        param_layout.addWidget(self.model_input, 0, 1)
        param_layout.addWidget(controller_label, 1, 0)
        param_layout.addWidget(self.controller_input, 1, 1)
        layout.addLayout(param_layout)
        
        # 文件夹操作
        folder_layout = QHBoxLayout()
        
        input_btn = QPushButton("打开输入文件夹")
        input_btn.setFont(QFont("Arial", 10))
        input_btn.setFixedSize(150, 38)
        input_btn.clicked.connect(self.open_v_conf_input_folder)
        input_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        output_btn = QPushButton("打开输出文件夹")
        output_btn.setFont(QFont("Arial", 10))
        output_btn.setFixedSize(150, 38)
        output_btn.clicked.connect(self.open_v_conf_output_folder)
        output_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        folder_layout.addWidget(input_btn)
        folder_layout.addWidget(output_btn)
        folder_layout.setSpacing(15)
        layout.addLayout(folder_layout)
        
        # 运行按钮
        run_btn = QPushButton("处理配置字表")
        run_btn.setFont(QFont("Arial", 12, QFont.Bold))
        run_btn.setFixedSize(180, 50)
        run_btn.clicked.connect(self.process_v_conf_table)
        run_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        # 居中放置按钮
        btn_container = QWidget()
        btn_layout = QHBoxLayout(btn_container)
        btn_layout.addStretch()
        btn_layout.addWidget(run_btn)
        btn_layout.addStretch()
        layout.addWidget(btn_container)
        
        return group
    
    def create_status_area(self, main_layout):
        """创建状态显示区域"""
        status_group = QGroupBox("状态信息")
        status_group.setFont(QFont("Arial", 11, QFont.Bold))
        status_layout = QVBoxLayout(status_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        # 状态文本
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(120)
        self.status_text.setReadOnly(True)
        self.status_text.setFont(QFont("Consolas", 9))
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                padding: 8px;
                font-size: 9pt;
                font-family: 'Consolas', monospace;
            }
        """)
        status_layout.addWidget(self.status_text)
        
        main_layout.addWidget(status_group)
    
    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        self.status_text.ensureCursorVisible()
    
    def show_progress(self, show=True):
        """显示/隐藏进度条"""
        self.progress_bar.setVisible(show)
        if show:
            self.progress_bar.setRange(0, 0)  # 无限进度条
        else:
            self.progress_bar.setRange(0, 1)
            self.progress_bar.setValue(1)
    
    def open_v_info_input_folder(self):
        """打开关键信息表输入文件夹"""
        try:
            self.core.open_folder(self.core.v_info_input_dir)
            self.log_message(f"打开关键信息表输入文件夹: {self.core.v_info_input_dir}")
        except Exception as e:
            self.log_message(f"打开关键信息表输入文件夹失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")
    
    def open_v_info_output_folder(self):
        """打开关键信息表输出文件夹（最新的子文件夹）"""
        try:
            latest_folder = self.core.get_latest_output_folder(self.core.v_info_output_dir)
            self.core.open_folder(latest_folder)
            self.log_message(f"打开关键信息表输出文件夹: {latest_folder}")
        except Exception as e:
            self.log_message(f"打开关键信息表输出文件夹失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")
    
    def open_v_conf_input_folder(self):
        """打开配置字表输入文件夹"""
        try:
            self.core.open_folder(self.core.v_conf_input_dir)
            self.log_message(f"打开配置字表输入文件夹: {self.core.v_conf_input_dir}")
        except Exception as e:
            self.log_message(f"打开配置字表输入文件夹失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")
    
    def open_v_conf_output_folder(self):
        """打开配置字表输出文件夹（最新的子文件夹）"""
        try:
            latest_folder = self.core.get_latest_output_folder(self.core.v_conf_output_dir)
            self.core.open_folder(latest_folder)
            self.log_message(f"打开配置字表输出文件夹: {latest_folder}")
        except Exception as e:
            self.log_message(f"打开配置字表输出文件夹失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")
    
    def process_v_info_table(self):
        """处理关键信息表"""
        try:
            if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
                self.log_message("有线程正在运行，请稍后")
                return
            
            self.show_progress(True)
            self.worker = VKIWorker("v_info_table", self.core)
            self.worker.progress.connect(self.log_message)
            self.worker.finished.connect(self.on_operation_finished)
            self.worker.start()
        except Exception as e:
            self.log_message(f"启动失败: {str(e)}")
            self.show_progress(False)
    
    def process_v_conf_table(self):
        """处理配置字表"""
        try:
            if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
                self.log_message("有线程正在运行，请稍后")
                return
            
            model, controller = self.core.get_user_inputs()
            self.show_progress(True)
            self.worker = VKIWorker("v_conf_table", self.core, model, controller)
            self.worker.progress.connect(self.log_message)
            self.worker.finished.connect(self.on_operation_finished)
            self.worker.start()
        except Exception as e:
            self.log_message(f"启动失败: {str(e)}")
            self.show_progress(False)
    
    def process_both(self):
        """同时处理两个表"""
        try:
            if (hasattr(self, 'v_info_worker') and self.v_info_worker and self.v_info_worker.isRunning()) or \
               (hasattr(self, 'v_conf_worker') and self.v_conf_worker and self.v_conf_worker.isRunning()):
                self.log_message("有线程正在运行，请稍后")
                return
            
            model, controller = self.core.get_user_inputs()
            self.show_progress(True)
            self.log_message("开始同时运行两个工具...")
            
            # 初始化计数器
            self.both_completed_count = 0
            self.both_success_count = 0
            
            # 创建两个独立的线程
            self.v_info_worker = VKIWorker("v_info_table", self.core)
            self.v_conf_worker = VKIWorker("v_conf_table", self.core, model, controller)
            
            # 连接信号
            self.v_info_worker.progress.connect(lambda msg: self.log_message(f"[关键信息表] {msg}"))
            self.v_conf_worker.progress.connect(lambda msg: self.log_message(f"[配置字表] {msg}"))
            
            self.v_info_worker.finished.connect(self.on_both_worker_finished)
            self.v_conf_worker.finished.connect(self.on_both_worker_finished)
            
            # 同时启动两个线程
            self.v_info_worker.start()
            self.v_conf_worker.start()
            
        except Exception as e:
            self.log_message(f"启动失败: {str(e)}")
            self.show_progress(False)
    
    def on_both_worker_finished(self, success, message):
        """两个工具同时运行时的完成回调"""
        try:
            self.both_completed_count += 1
            if success:
                self.both_success_count += 1
            
            self.log_message(message)
            
            # 检查是否两个都完成了
            if self.both_completed_count >= 2:
                self.show_progress(False)
                
                # 清理线程引用
                if hasattr(self, 'v_info_worker'):
                    if self.v_info_worker.isRunning():
                        self.v_info_worker.wait(1000)
                    self.v_info_worker = None
                
                if hasattr(self, 'v_conf_worker'):
                    if self.v_conf_worker.isRunning():
                        self.v_conf_worker.wait(1000)
                    self.v_conf_worker = None
                
                # 显示最终结果
                if self.both_success_count == 2:
                    QTimer.singleShot(100, lambda: self._show_result_message(True, "两个表格处理完成"))
                elif self.both_success_count == 1:
                    QTimer.singleShot(100, lambda: self._show_result_message(False, "一个表格处理成功，一个失败"))
                else:
                    QTimer.singleShot(100, lambda: self._show_result_message(False, "两个表格处理都失败"))
                
        except Exception as e:
            self.log_message(f"同时运行回调异常: {str(e)}")
    
    def on_operation_finished(self, success, message):
        """单个操作完成回调"""
        try:
            self.show_progress(False)
            self.log_message(message)
            
            # 确保线程正常结束
            if hasattr(self, 'worker') and self.worker:
                if self.worker.isRunning():
                    self.worker.wait(1000)
                self.worker = None
            
            QTimer.singleShot(100, lambda: self._show_result_message(success, message))
                
        except Exception as e:
            self.log_message(f"操作完成回调异常: {str(e)}")
            QMessageBox.critical(self, "错误", f"回调异常: {str(e)}")
    
    def _show_result_message(self, success, message):
        """显示结果消息"""
        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.critical(self, "错误", message)


def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    window = VKIGUI()
    window.show()
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()