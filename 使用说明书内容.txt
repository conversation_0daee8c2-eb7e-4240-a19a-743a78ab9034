VSETools 使用说明


1    快速开始
1.1    安装部署
1.  将项目压缩包解压到合适的位置（建议解压到 D 盘或其他非系统盘）
2.  双击运行脚本：

create_shortcut .bat
3.  桌面将自动生成”VSETools1.0” 快捷方式
4.  双击快捷方式即可启动程序，无需安装其他软件

1.2    主界面功能
VSETools 主页提供两个核心功能入口：
•  自动文件处理：DMS 车型文件的自动化管理系统
•  自动软件开发：包含软件标定、软件集成、 DM 制作、RTE 点检四大功能模块

2    详细使用指南
2.1    自动文件处理
2.1.1    车型设置
1.  在” 车型代号” 输入框中输入目标车型代号
2.  点击” 设置车型” 按钮，系统将自动创建车型文件夹及初始内容
3.  点击” 车型文件夹” 按钮，进入车型信息配置界面

2.1.2    人员信息配置
1.  打开 information 文件夹中的 Excel 文件，如：

图1  人员信息模板
2. 参照 E1 单元格的填写说明，录入相关人员信息

图2  填写说明
3.  主要填写内容：各人员的邮箱或工号
4.  填写规则：每个单元格填写一个邮箱，多人信息依次向右填写
5.  完成后保存文件

2.1.3    文件自动处理
1.  在” 文件操作” 区域选择处理模式（可选择静默模式后台运行）
2.  勾选所需处理的文件类型（如 DVP、PPL 等）
3.  点击” 执行文件处理”，系统将自动完成申请编号和内容填充

图3  申请编号和填写内容

2.1.4    上传审批流程
1.  点击” 打开审批文件夹” 按钮
2.  将待审批文件放入文件夹（需同时提供源文件和对应 PDF 文件）
3.  选择运行模式（可选择静默模式）
4.  点击” 上传审批文件” 按钮，系统自动处理（每份文件约需几分钟）

图4  上传审批文件

2.2    自动软件开发 - DM 制作工具 - 关键信息表和配置字表处理工具
2.2.1    功能入口
依次点击：自动软件开发 → DM 制作 → 关键信息表和配置字表处理工具

2.2.2    关键信息表处理工具
功能说明：将项目主管提供的底盘关键信息表转换为 DM 模块所需格式 操作步骤：
1.  点击” 打开输入文件夹”，将底盘关键信息表文件放入（支持批量处理）
2.  点击” 处理关键信息表” 按钮
3.  系统自动转换并填充到标准模板中

图5  关键信息表处理工具

2.2.3    配置字表处理工具
功能说明：从配置字总表中提取指定车型的配置信息并填充模板 操作步骤：
1.  点击” 打开输入文件夹”，放入配置字总表文件（支持多文件）
2.  输入目标车型代号
3.  选择控制器类型（D2/D3/域控/OneBox）
4.  点击” 处理配置表” 按钮
5.  处理完成后，点击” 打开输出文件夹” 查看结果



图6 配置字表处理工具
2.2.4    批量处理模式
同时运行两个工具：
1.  在关键信息表工具的输入文件夹中放入相关文件
2.  在配置字表工具区域完成文件放置和参数设置
3.  点击” 同时运行两个工具” 按钮
4.  通过状态信息窗口实时监控处理进度

图7  同时运行功能和状态信息
2.3  RTE自动化点检功能
该功能可以自动的操作Canape和Canoe对刷入控制器的VSE软件进行点检，绘制信号输出结果，并生成点检报告。
2.3.1    UI输入
该功能的输入见图8。

图8.RTE自动化点检功能UI输入截图
其中包括：
控制器型号
该输入框是一个下拉列表，列表中包括D2，D3，OBA，域控四个控制器型号。根据点检的控制器类型选择即可。
实车数据文件夹
存放实车数据的文件夹。用户将需要回灌点检的实车数据存放到指定文件夹中，然后点击浏览按钮，出现文档浏览器，在文档浏览器中选择该文件夹，并点击选择文件夹按钮。

图9.实车数据选择操作截图
输出结果保存文件夹
存放生成的点检报告和点检结果图的文件夹。用户首先需要创建一个空白的文件夹，然后点击浏览按钮，出现文档浏览器，将创建的的文件夹路径添加到文档浏览器中，并点击选择文件夹按钮。

图10.输出结果保存操作截图
Elf文件保存地址
获取代码编译生的elf存放地址。点击对应的浏览按钮，出现文件浏览器，选择目标elf文件，然后点击open按钮。

图10.获取elf文件地址操作截图
2.3.2   使用方法
UI输入框中的信息补充完整，点击开始点检按钮，点检开始。点检过程中将会弹出点检进度条，方便用户了解点检执行进展。当点检完成后，输出结果保存文件夹中生成的文件见图11。

图11.RTE点检输出结果图
2.4  车型DM自动化开发功能
该功能可以自动地根据DM接口文件，DM标定量表，关键信息表等文件自动的生成相应车型的DM文件，其中包括DM的simulink模型，数据字典，C代码和差异性分析报告。
2.4.1    UI输入
 	该功能的UI输入见图12。

图12.DM开发UI输入图
版本号
该文本框需要手动输入车型DM的版本，其格式为VSEX.XX.XX(X为0~9的数)。
车型名称
该文本框需要手动输入车型名称，格式为车型代号-控制器。
输入文件夹
用户需要创建一个空白文件夹，并放入生成车型DM的必要文件。必要文件见图13。
图13.生成车型DM需提供文件截图
这些文件包括2.00.41或2.00.51的平台释放DM模型：VSE_VehDataMngt.slx，DM数据字典：VSE_VehDataMngt.sldd和VSE_ComnDD.sldd，车型的DM标定量表：VSE_DM标定量表.xlsx，车型的接口文件：VSE_DM接口文件.xlsx，车型的电控关键信息表：VSEAA2.0-SWE1-014-VSE2.0项目XX-XX底盘电控关系信息.xlsx。
输出文件夹
用于存放生成的车型DM文件。用户需创建一个空白的文件夹用于存放生成的车型DM的相关文件。创建空白文件夹后，点击浏览按钮，弹出文件夹浏览器，选择创建的空白文件夹，点击选择文件夹，便可获得文件夹地址。

图14.添加输出文件操作流程截图
2.4.2    使用方法
	UI输入框中的信息补充完整，点击开始制作按钮，便会弹出制作进度条。当进度条达到100%，则车型DM文件制作完成。生成的文件效果图见图15。

图15.生成车型DM文件截图
生成的文件内容包括车型DM的C代码，车型DM的模型和数据字典，车型差异性分析报告，车型关键参数比较结果。
2.5 自动化集成功能
	该功能用于将VSE各个功能模块整合成一个集成模型用于开发人员后续模型回灌调试。同时也提供集成代码回灌的功能，用户可以自定义选择需要记录的信号，然后提供实车csv数据，便可仿真观测信号的输出结果。
2.5.1    UI输入
模型集成输入
用于VSE模型集成的UI输入见图16。该功能可以将各个模块整合成一个集成大模型，该集成模型用于VSE开发人员回灌分析调试。同时，它附带能够将车型CANMatrix.xlsx转换成dbc文件的功能。

图16.模型集成UI输入截图
VSE模型存放地址
用于存放需要集成的各个模块的模型和数据字典，待转换的CANMatrix.xlsx(可选)。用户首先需要创建一个空白的文件夹，并将所需的文件放入该文件夹中。具体所需的文件见图17。

图17.模型集成输入文件夹内容截图
其中内容包括SVN中受控的各个功能模块的受控文件夹，车辆CAN矩阵表。提供相应的文件后，点击浏览按钮，弹出文件夹浏览器，将该文件夹选中，点击选择文件夹按钮即可。执行流程见图18。

图18.导入输出文件地址流程截图
代码集成测试输入
代码集成测试的UI见图19。

图19.代码集成测试UI输入截图
信号选择器
用户可以自定义的在信号选择窗口添加多个信号，鼠标左击选中，操作见图20。

图20.选择信号操作截图
由图可知，已选中三个信号。当被选中的信号再鼠标左击一次便可取消选中。见图21。

图21.信号取消选择操作截图
当选中信号，右击鼠标弹出选择窗口。可执行的操作包括添加信号和取消选择。具体展示见图22。

图22.鼠标右击操作截图
当选择取消选择，三个已选信号，则将取消被选左中。
当点击添加信号，绘制图片区域将添加一组绘制配置，展示见图23。

图23.添加图片操作截图
对已添加的绘制配置，可对其鼠标右击，弹出取消绘制弹窗，单击取消绘制便可取消绘制该组信号。

图24.取消绘制操作截图
点击某个信号名，进行取消绘制，则只会删掉该信号。

图25.删掉一个信号操作截图
点击绘制图片名入pic_1（见图23中），进行取消绘制，则整个图片中所有信号都会删除。

图26.删掉所有信号操作截图
CSV测试数据文件夹
用于存放实车CSV文件。用户创建空白文件夹，将用于测试的实车CSV文件放入该文件夹中，点击浏览按钮，将该文件夹导入到文本框中。

图27.创建测试数据文件夹
生成仿真图片文件夹
用于存放仿真结果图。用户创建空白文件夹，点击浏览按钮，将该文件夹导入到文本框中。
2.5.2    使用方法
	UI输入如图28补充完整，点击开始仿真按钮便可进行代码仿真。

图28.执行代码仿真图
当仿真结束，在生成仿真图片文件夹中便可分析回灌图片。

图29.代码仿真结果展示
3    使用提示
•  所有操作均在统一界面内完成，避免多窗口切换
•  支持批量文件处理，提高工作效率
•  静默模式适合后台处理大量文件
•  实时状态显示让处理过程更加透明