from types import NoneType
from uuid import uuid4

import pandas as pd

import os
from lxml import etree
import shutil
from loguru import logger

import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)
import excelUtil

sldd_signal_set=set()
sldd_signal_uuid_set=set()
def merge_sldd(source_sldd, merge_sldd):
    unzip_dir_path = f"{source_sldd}UnZipDir"
    if os.path.exists(unzip_dir_path):
        shutil.rmtree(unzip_dir_path)
    excelUtil.unzip_file(source_sldd, unzip_dir_path)

    source_sldd_xml_path = os.path.join(unzip_dir_path, 'data', 'chunk0.xml')
    merge_sldd_xml_path = os.path.join(merge_sldd, 'data', 'chunk0.xml')

    parser = etree.XMLParser(
        load_dtd=False,  # 不加载 DTD
        no_network=True,  # 禁止网络请求（防止自动下载 DTD）
        dtd_validation=False,  # 关闭 DTD 校验
        attribute_defaults=False,
        recover=True
    )
    source_sldd_tree = etree.parse(source_sldd_xml_path, parser=parser)
    merge_sldd_tree = etree.parse(merge_sldd_xml_path, parser=parser)

    source_sldd_root = source_sldd_tree.getroot()
    merge_sldd_root = merge_sldd_tree.getroot()

    merge_sldd_data: lxml.etree._Element=merge_sldd_root.findall('Object')
    source_sldd_data: lxml.etree._Element= source_sldd_root.findall('Object')
    for item in source_sldd_data:
        pName= item.find('P[@Name="Name"]')
        pUUID=item.find('P[@Name="UUID"]')
        if pName is not None:
            if pName.text not in sldd_signal_set:
                if pUUID.text in sldd_signal_uuid_set:
                    pUUID.text=str(uuid4())
                sldd_signal_uuid_set.add(pUUID.text)
                merge_sldd_root.append(item)
                sldd_signal_set.add(pName.text)
        else:
            continue

    merge_sldd_tree.write(merge_sldd_xml_path)

if __name__ == '__main__':
    mergeSLDDPath="itegration.slddUnZipDir"
    excelUtil.unzip_file("itegration.sldd",mergeSLDDPath)

    merge_sldd("VSE_CnvArbn.sldd",mergeSLDDPath)
