# create_new_table.py
import pandas as pd
import sqlite3
import numpy as np
from utils import has_number, count_numbers
from config import NEW_TABLE_COLUMNS, NEW_TABLE_DATA, MAPPING, DATABASE_PATH

def process_steering_params(new_df, vehicle_columns):
    """处理转向角度参数，确保在依赖值填充后再计算"""
    steer_frnt_idx = new_df[new_df["信号定义"] == "ca_VSE_DM_fSteerFrntAg"].index[0]
    steer_in_idx = new_df[new_df["信号定义"] == "ca_VSE_DM_fWhlSteerAgInFrnt"].index[0]
    steer_out_idx = new_df[new_df["信号定义"] == "ca_VSE_DM_fWhlSteerAgOutFrnt"].index[0]
    steer_rat_idx = new_df[new_df["信号定义"] == "ca_VSE_DM_fSteerRat"].index[0]

    # 生成 ca_VSE_DM_fSteerFrntAg 的 50 个值
    steer_frnt_values = np.linspace(0, 720, 50).tolist()

    for vehicle_col in vehicle_columns:
        steer_ratio = new_df.at[steer_rat_idx, vehicle_col]
        try:
            steer_ratio = float(steer_ratio)
        except (ValueError, TypeError):
            print(f"{vehicle_col} 的转向传动比无效，填充 'VKI中无'")
            new_df.at[steer_frnt_idx, vehicle_col] = "VKI中无"
            new_df.at[steer_in_idx, vehicle_col] = "VKI中无"
            new_df.at[steer_out_idx, vehicle_col] = "VKI中无"
            continue

        # 填充 ca_VSE_DM_fSteerFrntAg（空格分隔）
        new_df.at[steer_frnt_idx, vehicle_col] = "[" + " ".join(f"{x:.4f}" for x in steer_frnt_values) + "]"

        # 计算内外侧车轮转角（空格分隔）
        steer_in_out_values = [x / steer_ratio for x in steer_frnt_values]
        new_df.at[steer_in_idx, vehicle_col] = "[" + " ".join(f"{x:.4f}" for x in steer_in_out_values) + "]"
        new_df.at[steer_out_idx, vehicle_col] = "[" + " ".join(f"{x:.4f}" for x in steer_in_out_values) + "]"

    return new_df

def create_new_table(db_path=DATABASE_PATH, new_table_name="new_vehicle_table"):
    """创建新表 new_vehicle_table"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 找到包含“关键信息”的依据表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    basis_table = None
    for table in tables:
        table_name = table[0]
        if "关键信息" in table_name:
            basis_table = table_name
            break

    if not basis_table:
        print("未找到包含‘关键信息’的依据表")
        conn.close()
        return

    # 读取依据表
    basis_df = pd.read_sql_query(f"SELECT * FROM {basis_table}", conn)

    # 确定车型列
    vehicle_columns = [col for col in basis_df.columns if "型" in col]
    if not vehicle_columns:
        raise ValueError(
            "未找到包含‘型’字的列名。请按照以下步骤修改关键信息表：\n"
            "1. 检查所有与车型相关的列名，在列名中添加‘型’字（例如：'车辆'改为'车辆型'）\n"
            "2. 如果车型列列名在合并单元格中，请拆分这些合并单元格\n"
            "3. 拆分后，使用原合并单元格的值填充所有新拆分的单元格，确保每个单元格都有相同的值\n"
            "修改完成后重新运行程序"
        )

    # 创建新表 DataFrame
    new_df = pd.DataFrame(NEW_TABLE_DATA, columns=NEW_TABLE_COLUMNS)
    for col in vehicle_columns:
        new_df[col] = None

    # 第一步：填充除质心距后轴距离和特定默认值外的所有值
    for idx, row in new_df.iterrows():
        signal_def = row["信号定义"]
        signal_name = row["信号名称"]
        mapping_info = MAPPING.get(signal_def, {})

        # 处理固定默认值
        default_value_signals = [
            "ca_VSE_DM_fFrntAxleR", "ca_VSE_DM_fReAxleR", "ca_VSE_DM_fStabyFac",
            "ca_VSE_DM_fKPLatOffsFrnt", "ca_VSE_DM_fKPLatOffsRe", "ca_VSE_DM_fDamprCoeff",
            "ca_VSE_DM_fDamprBalPosnFL", "ca_VSE_DM_fDamprBalPosnFR",
            "ca_VSE_DM_fDamprBalPosnRL", "ca_VSE_DM_fDamprBalPosnRR",
            "ca_VSE_DM_fAgtoHeit_FL", "ca_VSE_DM_fAgtoHeit_FR",
            "ca_VSE_DM_fAgtoHeit_RL", "ca_VSE_DM_fAgtoHeit_RR"
        ]
        if signal_def in default_value_signals:
            for vehicle_col in vehicle_columns:
                new_df.at[idx, vehicle_col] = mapping_info["默认值"]
            continue

        # 跳过转向角度参数，留待最后处理
        if signal_def in ["ca_VSE_DM_fSteerFrntAg", "ca_VSE_DM_fWhlSteerAgInFrnt", "ca_VSE_DM_fWhlSteerAgOutFrnt"]:
            continue

        if not mapping_info:
            print(f"{signal_def} 无对应关系，填充 'VKI中无'")
            for vehicle_col in vehicle_columns:
                new_df.at[idx, vehicle_col] = "VKI中无"
            continue

        desc1_condition = mapping_info.get("描述1")
        desc2_condition = mapping_info.get("描述2")
        desc3_condition = mapping_info.get("描述3")
        unit_convert = mapping_info.get("单位转换")
        special_process = mapping_info.get("特殊处理")

        if signal_def in ["ca_VSE_DM_fCentroidLenBFull", "ca_VSE_DM_fCentroidLenB"]:
            continue

        # 灵活匹配
        matches = basis_df
        if desc1_condition:
            matches = matches[matches["描述1"] == desc1_condition]
        if desc2_condition:
            matches = matches[matches["描述2"] == desc2_condition]
        if desc3_condition:
            matches = matches[matches["描述3"] == desc3_condition]

        if matches.empty and signal_name:
            matches = basis_df[basis_df["描述2"].str.contains(signal_name, na=False)]

        if matches.empty:
            print(f"{signal_def} 未匹配到数据：描述1={desc1_condition}, 描述2={desc2_condition}, 描述3={desc3_condition}, 信号名称={signal_name}")
            for vehicle_col in vehicle_columns:
                if signal_def in [
                    "ca_VSE_DM_fFL_Posn_X", "ca_VSE_DM_fFR_Posn_X", "ca_VSE_DM_fFL_Posn_Y",
                    "ca_VSE_DM_fFR_Posn_Y", "ca_VSE_DM_fRL_Posn_X", "ca_VSE_DM_fRL_Posn_Y",
                    "ca_VSE_DM_fRR_Posn_X", "ca_VSE_DM_fRR_Posn_Y"
                ]:
                    new_df.at[idx, vehicle_col] = None  # 待后续处理
                else:
                    new_df.at[idx, vehicle_col] = "VKI中无"
            continue

        if len(matches) > 1:
            print(f"{signal_def} 匹配到多个值 ({len(matches)})，使用最后一个")

        match_row = matches.iloc[-1]

        for vehicle_col in vehicle_columns:
            value = match_row.get(vehicle_col, None)
            if pd.isna(value) or value is None:
                print(f"{signal_def} 在 {vehicle_col} 无数据，填充 'VKI中无'")
                new_df.at[idx, vehicle_col] = "VKI中无"
                continue

            if special_process:
                try:
                    value = special_process(value)
                except (AttributeError, ValueError):
                    value = "VKI中无"
            elif unit_convert:
                try:
                    value = float(value)
                    value = unit_convert(value)
                except (ValueError, TypeError):
                    if has_number(value):
                        print(f"{signal_def} 在 {vehicle_col} 的值 {value} 无法转换为数值，但含数字，保留原始值")
                    else:
                        print(f"{signal_def} 在 {vehicle_col} 的值 {value} 无法转换为数值且不含数字，填充 'VKI中无'")
                        new_df.at[idx, vehicle_col] = "VKI中无"
                        continue

            if not has_number(value):
                print(f"{signal_def} 在 {vehicle_col} 的最终值 {value} 不含数字，填充 'VKI中无'")
                new_df.at[idx, vehicle_col] = "VKI中无"
                continue

            new_df.at[idx, vehicle_col] = value

    # 第二步：处理质心距后轴距离
    for signal_def in ["ca_VSE_DM_fCentroidLenBFull", "ca_VSE_DM_fCentroidLenB"]:
        idx = new_df[new_df["信号定义"] == signal_def].index[0]
        axle_row = new_df[new_df["信号定义"] == "ca_VSE_DM_fAxleDst"]
        front_row = new_df[new_df["信号定义"] == (
            "ca_VSE_DM_fCentroidLenAFull" if signal_def == "ca_VSE_DM_fCentroidLenBFull" else "ca_VSE_DM_fCentroidLenA")]

        if axle_row.empty or front_row.empty:
            print(f"{signal_def} 未找到轴距或前轴距离行，填充 'VKI中无'")
            for vehicle_col in vehicle_columns:
                new_df.at[idx, vehicle_col] = "VKI中无"
            continue

        for vehicle_col in vehicle_columns:
            axle_value = axle_row[vehicle_col].values[0]
            front_value = front_row[vehicle_col].values[0]
            if pd.notna(axle_value) and pd.notna(front_value) and axle_value != "VKI中无" and front_value != "VKI中无":
                try:
                    value = float(axle_value) - float(front_value)
                    new_df.at[idx, vehicle_col] = value
                except (ValueError, TypeError):
                    print(f"{signal_def} 在 {vehicle_col} 的轴距 {axle_value} 或前轴距离 {front_value} 无法计算，填充 'VKI中无'")
                    new_df.at[idx, vehicle_col] = "VKI中无"
            else:
                print(f"{signal_def} 在 {vehicle_col} 缺少轴距或前轴距离数据，填充 'VKI中无'")
                new_df.at[idx, vehicle_col] = "VKI中无"

    # 第三步：处理 ECU 位置参数
    for signal_def, default_signal, sign in [
        ("ca_VSE_DM_fFL_Posn_X", "ca_VSE_DM_fCentroidLenA", "+"),
        ("ca_VSE_DM_fFL_Posn_Y", "ca_VSE_DM_fWhlWidthFrnt", "+"),
        ("ca_VSE_DM_fFR_Posn_X", "ca_VSE_DM_fCentroidLenA", "+"),
        ("ca_VSE_DM_fFR_Posn_Y", "ca_VSE_DM_fWhlWidthFrnt", "-"),
        ("ca_VSE_DM_fRL_Posn_X", "ca_VSE_DM_fCentroidLenB", "-"),
        ("ca_VSE_DM_fRL_Posn_Y", "ca_VSE_DM_fWhlWidthRe", "+"),
        ("ca_VSE_DM_fRR_Posn_X", "ca_VSE_DM_fCentroidLenB", "-"),
        ("ca_VSE_DM_fRR_Posn_Y", "ca_VSE_DM_fWhlWidthRe", "-"),
    ]:
        idx = new_df[new_df["信号定义"] == signal_def].index[0]
        default_row = new_df[new_df["信号定义"] == default_signal]
        if default_row.empty:
            print(f"{signal_def} 未找到默认值信号 {default_signal}，填充 'VKI中无'")
            for vehicle_col in vehicle_columns:
                new_df.at[idx, vehicle_col] = "VKI中无"
            continue

        for vehicle_col in vehicle_columns:
            value = new_df.at[idx, vehicle_col]
            if value is None or value == "VKI中无":
                default_value = default_row[vehicle_col].values[0]
                if pd.notna(default_value) and default_value != "VKI中无":
                    try:
                        if signal_def in ["ca_VSE_DM_fFL_Posn_Y", "ca_VSE_DM_fFR_Posn_Y", "ca_VSE_DM_fRL_Posn_Y",
                                          "ca_VSE_DM_fRR_Posn_Y"]:
                            value = float(default_value) / 2 * (1 if sign == "+" else -1)
                        else:
                            value = float(default_value) * (1 if sign == "+" else -1)
                        new_df.at[idx, vehicle_col] = value
                    except (ValueError, TypeError):
                        print(f"{signal_def} 在 {vehicle_col} 的默认值 {default_value} 无法计算，填充 'VKI中无'")
                        new_df.at[idx, vehicle_col] = "VKI中无"
                else:
                    print(f"{signal_def} 在 {vehicle_col} 无有效默认值，填充 'VKI中无'")
                    new_df.at[idx, vehicle_col] = "VKI中无"

    # 第四步：处理转向角度参数
    new_df = process_steering_params(new_df, vehicle_columns)

    # 第五步：检查转向角度参数的维度一致性
    steer_params = ["ca_VSE_DM_fSteerFrntAg", "ca_VSE_DM_fWhlSteerAgInFrnt", "ca_VSE_DM_fWhlSteerAgOutFrnt"]
    steer_indices = [new_df[new_df["信号定义"] == param].index[0] for param in steer_params]

    for vehicle_col in vehicle_columns:
        dimensions = [count_numbers(new_df.at[idx, vehicle_col]) for idx in steer_indices if
                      new_df.at[idx, vehicle_col] not in ["VKI中无"]]
        if dimensions and len(set(dimensions)) > 1:
            print(f"{vehicle_col} 中转向角度参数维度不一致，填充 'VKI数据有误'")
            for idx in steer_indices:
                new_df.at[idx, vehicle_col] = "VKI数据有误"

    # 将新表存入数据库
    new_df.to_sql(new_table_name, conn, if_exists="replace", index=False)
    print(f"新表 '{new_table_name}' 已成功写入数据库 {db_path}")

    conn.close()

if __name__ == "__main__":
    create_new_table()