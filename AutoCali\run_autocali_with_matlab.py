"""
AutoCali专用启动脚本
使用Python 3.8环境运行需要matlab.engine的部分
"""
import os
import sys
import subprocess
from pathlib import Path


def get_python38_path():
    """获取Python 3.8路径"""
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 检查WinPython 3.8路径
    python38_path = project_root / "Winpython30810" / "python-3.8.10.amd64" / "python.exe"
    
    if python38_path.exists():
        return str(python38_path)
    else:
        print(f"警告: 未找到Python 3.8环境: {python38_path}")
        return sys.executable  # 使用当前Python作为备用


def run_p2s_with_python38(mat_output_dir):
    """使用Python 3.8运行P2S.py"""
    try:
        # 获取P2S.py路径
        autocali_dir = Path(__file__).parent
        p2s_path = autocali_dir / "simTrans" / "P2S.py"
        
        if not p2s_path.exists():
            print(f"错误: P2S.py文件不存在: {p2s_path}")
            return False
        
        # 获取Python 3.8路径
        python38_exe = get_python38_path()
        print(f"使用Python环境: {python38_exe}")
        print(f"运行P2S.py: {p2s_path}")
        print(f"MAT输出目录: {mat_output_dir}")
        
        # 运行P2S.py
        result = subprocess.run([
            python38_exe, str(p2s_path), mat_output_dir
        ], capture_output=True, text=True, cwd=str(autocali_dir))
        
        print("P2S.py输出:")
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"运行P2S.py时出错: {str(e)}")
        return False


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python run_autocali_with_matlab.py <mat_output_directory>")
        sys.exit(1)
    
    mat_output_dir = sys.argv[1]
    success = run_p2s_with_python38(mat_output_dir)
    
    if success:
        print("MAT转换完成")
        sys.exit(0)
    else:
        print("MAT转换失败")
        sys.exit(1)