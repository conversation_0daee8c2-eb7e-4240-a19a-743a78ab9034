import datetime

from asammdf import MDF
from py_canoe import <PERSON><PERSON><PERSON>, wait
import os
import win32com.client
from a2lUpdater import updateA2lByElf
import matplotlib.pyplot as plt
from pylab import mpl
from embed_image import insert_image2Excel
from collections import deque

mpl.rcParams["font.sans-serif"] = ["SimHei"]
mpl.rcParams["axes.unicode_minus"] = False


def get_current_time(fmt="%Y-%m-%d_%H-%M-%S"):
    return datetime.datetime.now().strftime(fmt)


files_Set = set()


def get_existFiles(path):
    for f in os.listdir(path):
        if f.endswith(".mf4") or f.endswith(".mdf") or f.endswith(".MDF"):
            files_Set.add(f)
    return


def get_recorderName(filePath):
    for f in os.listdir(filePath):
        if f.endswith(".mf4") or f.endswith(".mdf") or f.endswith(".MDF"):
            if f not in files_Set:
                files_Set.add(f)
                return f
    return




if __name__ == '__main__':

    canoe_path = r"C:\Users\<USER>\Downloads\Configuration1.cfg"
    canape_path = r"D:\hcem_vse0913\hcem_vse0912"
    Al2_filename = r"0824.A2L"
    elf_file_path = r"E:\ESES_VSE_0528\ESEA4.00.03（1）0527\Project\DefaultBuild\D3_APP.elf"
    folderPath = r"E:\新建文件夹\2"
    image_path = r"E:\新建文件夹\ESEA_D3降负载1"
    VSE_VERSION=r"VSE.2.00.40"

    IMU_Message = "IMU_0x051"
    Ax_Name = "IMU6_Ax"

    if not os.path.exists(image_path):
        os.makedirs(image_path)
    '''
    canoe初始化
    '''

    canoe_inst = CANoe()
    canoe_inst.open(canoe_cfg=canoe_path)

    '''
    canape初始化
    '''
    canape = win32com.client.Dispatch('CANape.Application')
    print('CANape dispatched.')
    updateA2lByElf(elf_file_path, os.path.join(canape_path, Al2_filename))
    canape.Open1(canape_path, 1, 50000, False)
    print('CANape initialized.')
    # Init CCP Device
    dev = canape.Devices.Add("XCPsim", os.path.join(canape_path, Al2_filename), "XCP", 1)

    print('Device \'XCPsim\' added.')
    XCP_1_Task = dev.Tasks("polling")
    XCP_1_Task.SamplingTime = 100

    XCP_1_Task.Channels.Add("VSE_RAC_fFLAdhCoeff")
    XCP_1_Task.Channels.Add("VSE_RAC_fFRAdhCoeff")
    XCP_1_Task.Channels.Add("VSE_RAC_fRLAdhCoeff")
    XCP_1_Task.Channels.Add("VSE_RAC_fRRAdhCoeff")
    XCP_1_Task.Channels.Add("g_VSE_VSRE_Out_Com_fVxEst")
    XCP_1_Task.Channels.Add("VSE_DM_fFLWhlSpd")
    XCP_1_Task.Channels.Add("VSE_DM_fFRWhlSpd")
    XCP_1_Task.Channels.Add("VSE_DM_fRLWhlSpd")
    XCP_1_Task.Channels.Add("VSE_DM_fRRWhlSpd")
    XCP_1_Task.Channels.Add("g_VSE_VSRE_Out_Com_fFLSlipRate")
    XCP_1_Task.Channels.Add("g_VSE_VSRE_Out_Com_fFRSlipRate")
    XCP_1_Task.Channels.Add("g_VSE_VSRE_Out_Com_fRLSlipRate")
    XCP_1_Task.Channels.Add("g_VSE_VSRE_Out_Com_fRRSlipRate")
    XCP_1_Task.Channels.Add("g_VSE_VME_Out_Com_fWhlVFEFL")
    XCP_1_Task.Channels.Add("g_VSE_VME_Out_Com_fWhlVFEFR")
    XCP_1_Task.Channels.Add("g_VSE_VME_Out_Com_fWhlVFERL")
    XCP_1_Task.Channels.Add("g_VSE_VME_Out_Com_fWhlVFERR")
    XCP_1_Task.Channels.Add("g_VSE_VME_Out_Com_fVehSprMEstim")
    XCP_1_Task.Channels.Add("g_VSE_VME_Out_Com_fVehMEstim")
    XCP_1_Task.Channels.Add("g_VSE_SSE_Out_Com_fSideSlipAg")
    XCP_1_Task.Channels.Add("g_VSE_SAE_Out_Com_fSlopAg")
    XCP_1_Task.Channels.Add("g_VSE_SYE_Out_Com_fSlopYAg")
    XCP_1_Task.Channels.Add("g_VSE_TYE_Out_Com_fTarWz")
    XCP_1_Task.Channels.Add("g_VSE_RACS_Out_Com_bSplitFlg")

    XCP_1_Task.Channels("VSE_RAC_fFLAdhCoeff").Save2MDF = 1
    XCP_1_Task.Channels("VSE_RAC_fFRAdhCoeff").Save2MDF = 1
    XCP_1_Task.Channels("VSE_RAC_fRLAdhCoeff").Save2MDF = 1
    XCP_1_Task.Channels("VSE_RAC_fRRAdhCoeff").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_VSRE_Out_Com_fVxEst").Save2MDF = 1
    XCP_1_Task.Channels("VSE_DM_fFLWhlSpd").Save2MDF = 1
    XCP_1_Task.Channels("VSE_DM_fFRWhlSpd").Save2MDF = 1
    XCP_1_Task.Channels("VSE_DM_fRLWhlSpd").Save2MDF = 1
    XCP_1_Task.Channels("VSE_DM_fRRWhlSpd").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_VSRE_Out_Com_fFLSlipRate").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_VSRE_Out_Com_fFRSlipRate").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_VSRE_Out_Com_fRLSlipRate").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_VSRE_Out_Com_fRRSlipRate").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_VME_Out_Com_fWhlVFEFL").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_VME_Out_Com_fWhlVFEFR").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_VME_Out_Com_fWhlVFERL").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_VME_Out_Com_fWhlVFERR").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_VME_Out_Com_fVehSprMEstim").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_VME_Out_Com_fVehMEstim").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_SSE_Out_Com_fSideSlipAg").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_SAE_Out_Com_fSlopAg").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_SYE_Out_Com_fSlopYAg").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_TYE_Out_Com_fTarWz").Save2MDF = 1
    XCP_1_Task.Channels("g_VSE_RACS_Out_Com_bSplitFlg").Save2MDF = 1

    '''
    canoe数据回放，canape数据记录
    
    '''

    get_existFiles(canape_path)
    testFiles = dict()
    # canoe_inst.set_replay_block_file(block_name='ReplayBlock 2', recording_file_path=)

    for file in os.listdir(folderPath):
        canoe_inst.start_measurement()
        canape.Measurement.Start()
        changeName = (file.split('.'))[0]
        wait(2)
        originalSigNums = canoe_inst.get_can_bus_statistics(1).get('bus_load')

        canoe_inst.set_replay_block_file(block_name='ReplayBlock 1', recording_file_path=folderPath + r'/' + file)
        canoe_inst.control_replay_block(block_name='ReplayBlock 1', start_stop=True)
        # canoe_inst.control_replay_block(block_name='ReplayBlock', start_stop=True)
        # canoe_inst.get_bus_nodes_info("CAN")
        # canoe_inst.check_signal_online("CAN", 1, "IMU_0x051", "IMU6_Ax")
        print('数据开始记录！！！')

        wait(3)
        recoderFileName = ""
        queue = deque()
        stop_flag=False
        while 1:
            # canoe_inst.check_signal_state("CAN", 1, "IMU_0x051", "IMU6_Ax")
            bus_statistics = canoe_inst.get_can_bus_statistics(1)
            ax = canoe_inst.get_signal_value("CAN", 1, IMU_Message, Ax_Name)
            nowSigNums = bus_statistics.get('bus_load')
            PeakLoad = bus_statistics.get('peak_load')
            if len(queue)<10:
                queue.append(ax)
            else:
                stop_flag = all(x == queue[0] for x in queue)
                queue.popleft()
                queue.append(ax)

            if (nowSigNums == originalSigNums or PeakLoad - nowSigNums > 1000) and stop_flag:
                print('数据结束记录！！！')
                canape.Measurement.Stop()
                recoderFileName = get_recorderName(canape_path)
                wait(2)
                signals = MDF(os.path.join(canape_path, recoderFileName)).to_dataframe()
                time = signals.index
                signals = signals.to_numpy()
                saveName = os.path.join(image_path, changeName)
                fig, axs = plt.subplots(2, 2)

                axs[0, 0].plot(time, signals[:, 0], color="blue", label="FL")
                axs[0, 0].legend(loc='best')

                axs[0, 1].plot(time, signals[:, 1], color="red", linestyle='--', label="FR")
                axs[0, 1].legend(loc='best')

                axs[1, 0].plot(time, signals[:, 2], color="yellow", linestyle=':', label="RL")
                axs[1, 0].legend(loc='best')

                axs[1, 1].plot(time, signals[:, 3], color="purple", linestyle='-.', label="RR")
                axs[1, 1].legend(loc='best')

                plt.tight_layout()

                plt.savefig(saveName + "路面附着系数.png")
                plt.close()

                plt.plot(time, signals[:, 4], color="green", label="车速")
                plt.plot(time, signals[:, 5], color="blue", label="左前轮速")
                plt.plot(time, signals[:, 6], color="red", linestyle='--', label="右前轮速")
                plt.plot(time, signals[:, 7], color="yellow", linestyle=':', label="左后轮速")
                plt.plot(time, signals[:, 8], color="purple", linestyle='-.', label="右后轮速")
                plt.legend(loc="best")
                plt.savefig(saveName + "车速.png")
                plt.close()

                fig, axs = plt.subplots(2, 2)

                axs[0, 0].plot(time, signals[:, 9], color="blue", label="左前滑移率")
                axs[0, 0].legend(loc='best')

                axs[0, 1].plot(time, signals[:, 10], color="red", linestyle='--', label="右前滑移率")
                axs[0, 1].legend(loc='best')

                axs[1, 0].plot(time, signals[:, 11], color="yellow", linestyle=':', label="左后滑移率")
                axs[1, 0].legend(loc='best')

                axs[1, 1].plot(time, signals[:, 12], color="purple", linestyle='-.', label="右后滑移率")
                axs[1, 1].legend(loc='best')

                plt.tight_layout()

                plt.savefig(saveName + "滑移率.png")
                plt.close()

                plt.plot(time, signals[:, 13], color="blue", label="左前轮垂直载荷")
                plt.plot(time, signals[:, 14], color="red", linestyle='--', label="右前轮垂直载荷")
                plt.plot(time, signals[:, 15], color="yellow", linestyle=':', label="左后轮垂直载荷")
                plt.plot(time, signals[:, 16], color="purple", linestyle='-.', label="右后轮垂直载荷")
                plt.legend(loc="best")
                plt.savefig(saveName + "车轮载荷.png")
                plt.close()

                plt.plot(time, signals[:, 17], color="red", label="簧上质量")
                plt.plot(time, signals[:, 18], color="blue", linestyle='--', label="整车质量")
                plt.legend(loc="best")
                plt.savefig(saveName + "质量.png")
                plt.close()

                plt.plot(time, signals[:, 19], color="blue", label="质心侧偏角")
                plt.legend(loc="best")
                plt.savefig(saveName + "质心侧偏角.png")
                plt.close()

                plt.plot(time, signals[:, 20], color="blue", label="纵坡")
                plt.legend(loc="best")
                plt.savefig(saveName + "纵坡.png")
                plt.close()

                plt.plot(time, signals[:, 21], color="blue", label="横坡")
                plt.legend(loc="best")
                plt.savefig(saveName + "横坡.png")
                plt.close()

                plt.plot(time, signals[:, 22], color="blue", label="目标横摆角速度")
                plt.legend(loc="best")
                plt.savefig(saveName + "目标横摆角速度.png")
                plt.close()

                plt.plot(time, signals[:, 23], color="blue", label="对开标志位")
                plt.legend(loc="best")
                plt.savefig(saveName + "对开标志位.png")
                plt.close()
                break
    canoe_inst.stop_measurement()
    # canoe_inst.quit()
    canape.quit()
    #insert_image2Excel( image_path,
    #r"E:\项目\新建文件夹 (2)\VSEAA2.0-SWE5-004-VSEAA 2.0 ST2H-域控车型V2.00.50集成点检报告V1.0.0 .xlsx")
